# 任务6-8: 剩余开发任务

## 任务6: 方案推荐算法开发

### 给Cursor/Augment的提示词

你是一个算法工程师，需要开发智能的单细胞测序实验方案推荐系统。

#### 核心算法需求
- **多目标优化算法**: 平衡成本、时间、质量三个维度
- **产品匹配引擎**: 基于需求特征匹配最适合的产品组合
- **方案评分系统**: 量化评估方案的可行性和优劣
- **个性化推荐**: 基于用户历史和偏好调整推荐

#### 算法实现
```typescript
interface RecommendationEngine {
  // 主推荐算法
  async generateRecommendations(
    requirements: UserRequirement,
    constraints: OptimizationConstraints
  ): Promise<RankedRecommendation[]>;
  
  // 产品匹配算法
  async matchProducts(
    tissueType: string,
    applicationGoals: string[],
    budget: number
  ): Promise<ProductMatch[]>;
  
  // 方案优化算法
  async optimizeProtocol(
    baseProtocol: Protocol,
    optimizationGoals: OptimizationGoal[]
  ): Promise<OptimizedProtocol>;
}

class SmartRecommendationEngine implements RecommendationEngine {
  async generateRecommendations(
    requirements: UserRequirement,
    constraints: OptimizationConstraints
  ): Promise<RankedRecommendation[]> {
    // 1. 特征工程
    const features = await this.extractFeatures(requirements);
    
    // 2. 候选方案生成
    const candidates = await this.generateCandidateProtocols(features);
    
    // 3. 多目标评分
    const scoredCandidates = await Promise.all(
      candidates.map(candidate => this.scoreProtocol(candidate, requirements, constraints))
    );
    
    // 4. 帕累托最优筛选
    const paretoOptimal = this.findParetoOptimal(scoredCandidates);
    
    // 5. 个性化排序
    const personalizedRank = await this.personalizeRanking(paretoOptimal, requirements.userId);
    
    return personalizedRank;
  }
  
  private async scoreProtocol(
    protocol: Protocol,
    requirements: UserRequirement,
    constraints: OptimizationConstraints
  ): Promise<ScoredProtocol> {
    const scores = {
      // 技术适配性评分 (0-1)
      technicalFit: await this.calculateTechnicalFit(protocol, requirements),
      
      // 成本效益评分 (0-1)
      costEfficiency: await this.calculateCostEfficiency(protocol, constraints.budget),
      
      // 时间效率评分 (0-1)
      timeEfficiency: await this.calculateTimeEfficiency(protocol, constraints.timeline),
      
      // 成功率评分 (0-1)
      successProbability: await this.predictSuccessRate(protocol, requirements),
      
      // 创新性评分 (0-1)
      innovation: await this.assessInnovation(protocol)
    };
    
    // 加权综合评分
    const weights = constraints.weights || {
      technicalFit: 0.3,
      costEfficiency: 0.25,
      timeEfficiency: 0.2,
      successProbability: 0.2,
      innovation: 0.05
    };
    
    const overallScore = Object.keys(scores).reduce(
      (sum, key) => sum + scores[key] * weights[key], 0
    );
    
    return {
      protocol,
      scores,
      overallScore,
      explanation: this.generateScoreExplanation(scores, weights)
    };
  }
}
```

---

## 任务7: 报告生成系统开发

### 给Cursor/Augment的提示词

你是一个报告生成专家，需要开发专业的单细胞测序实验方案报告生成系统。

#### 报告生成需求
- **多格式输出**: PDF、Word、HTML、PowerPoint
- **动态图表**: 成本分析图表、时间甘特图、流程图
- **模板化系统**: 支持多种报告模板和自定义样式
- **批量生成**: 支持批量报告生成和自动化流程

#### 实现架构
```typescript
interface ReportGenerator {
  async generateProtocolReport(
    protocol: Protocol,
    template: ReportTemplate,
    options: ReportOptions
  ): Promise<GeneratedReport>;
  
  async generateCostAnalysisReport(
    protocols: Protocol[],
    comparisonMode: 'detailed' | 'summary'
  ): Promise<GeneratedReport>;
  
  async generateExecutiveSummary(
    projectData: ProjectData
  ): Promise<GeneratedReport>;
}

class ProfessionalReportGenerator implements ReportGenerator {
  private pdfGenerator: PDFGenerator;
  private chartGenerator: ChartGenerator;
  private templateEngine: TemplateEngine;
  
  async generateProtocolReport(
    protocol: Protocol,
    template: ReportTemplate,
    options: ReportOptions
  ): Promise<GeneratedReport> {
    // 1. 数据预处理
    const reportData = await this.prepareReportData(protocol);
    
    // 2. 生成图表
    const charts = await this.generateCharts(reportData);
    
    // 3. 应用模板
    const renderedContent = await this.templateEngine.render(template, {
      ...reportData,
      charts,
      options
    });
    
    // 4. 生成最终文档
    const document = await this.generateDocument(renderedContent, options.format);
    
    return {
      documentId: this.generateDocumentId(),
      format: options.format,
      content: document,
      metadata: {
        generatedAt: new Date(),
        protocolId: protocol._id,
        template: template.name,
        pageCount: await this.getPageCount(document)
      }
    };
  }
  
  private async generateCharts(data: ReportData): Promise<ChartCollection> {
    return {
      costBreakdown: await this.chartGenerator.createPieChart({
        title: '成本构成分析',
        data: data.costAnalysis.breakdown,
        type: 'pie'
      }),
      
      timeline: await this.chartGenerator.createGanttChart({
        title: '项目时间安排',
        tasks: data.timeline.phases,
        type: 'gantt'
      }),
      
      workflow: await this.chartGenerator.createFlowChart({
        title: '实验流程图',
        steps: data.experimentalSteps,
        type: 'flowchart'
      })
    };
  }
}

// PDF生成器
class PDFGenerator {
  private puppeteer: Browser;
  
  async generatePDF(htmlContent: string, options: PDFOptions): Promise<Buffer> {
    const page = await this.puppeteer.newPage();
    
    await page.setContent(htmlContent, {
      waitUntil: 'networkidle0'
    });
    
    const pdf = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '20mm',
        bottom: '20mm',
        left: '15mm',
        right: '15mm'
      },
      ...options
    });
    
    await page.close();
    return pdf;
  }
}
```

---

## 任务8: 部署和优化

### 给Cursor/Augment的提示词

你是一个DevOps工程师，需要为单细胞测序方案生成系统设计完整的部署和运维方案。

#### 部署架构
- **容器化部署**: Docker + Kubernetes
- **微服务架