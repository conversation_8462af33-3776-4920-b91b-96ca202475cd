"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

export type UserRole = "super_admin" | "sales" | "customer" | "operations" | "guest"

export interface Permission {
  id: string
  name: string
  description: string
  roles: UserRole[]
}

export interface User {
  id: string
  name: string
  email: string
  role: User<PERSON>ole
  avatar?: string
  department?: string
  lastActive?: string
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  permissions: Permission[]
  hasPermission: (permissionId: string) => boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => void
}

const defaultPermissions: Permission[] = [
  {
    id: "view_dashboard",
    name: "查看仪表盘",
    description: "允许用户查看数据分析仪表盘",
    roles: ["super_admin", "sales"],
  },
  {
    id: "manage_customers",
    name: "管理客户",
    description: "允许用户添加、编辑和删除客户信息",
    roles: ["super_admin", "sales"],
  },
  {
    id: "view_customers",
    name: "查看客户",
    description: "允许用户查看客户信息",
    roles: ["super_admin", "sales", "operations"],
  },
  {
    id: "manage_knowledge",
    name: "管理知识库",
    description: "允许用户添加、编辑和删除知识库内容",
    roles: ["super_admin", "operations"],
  },
  {
    id: "view_knowledge",
    name: "查看知识库",
    description: "允许用户查看知识库内容",
    roles: ["super_admin", "sales", "operations", "customer"],
  },
  {
    id: "manage_solutions",
    name: "管理解决方案",
    description: "允许用户创建和编辑解决方案",
    roles: ["super_admin", "sales", "operations"],
  },
  {
    id: "view_solutions",
    name: "查看解决方案",
    description: "允许用户查看解决方案",
    roles: ["super_admin", "sales", "operations", "customer"],
  },
  {
    id: "manage_users",
    name: "管理用户",
    description: "允许用户添加、编辑和删除用户账户",
    roles: ["super_admin"],
  },
  {
    id: "manage_permissions",
    name: "管理权限",
    description: "允许用户配置系统权限",
    roles: ["super_admin"],
  },
]

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [permissions, setPermissions] = useState<Permission[]>(defaultPermissions)

  // Simulate loading user from storage or API
  useEffect(() => {
    const loadUser = async () => {
      try {
        // In a real app, this would be an API call or localStorage check
        const savedUser = localStorage.getItem("cellforge_user")
        if (savedUser) {
          setUser(JSON.parse(savedUser))
        }
      } catch (error) {
        console.error("Failed to load user:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadUser()
  }, [])

  const hasPermission = (permissionId: string) => {
    if (!user) return false
    const permission = permissions.find((p) => p.id === permissionId)
    return permission ? permission.roles.includes(user.role) : false
  }

  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Demo users for different roles
      const demoUsers: Record<string, User> = {
        "<EMAIL>": {
          id: "1",
          name: "管理员",
          email: "<EMAIL>",
          role: "super_admin",
          avatar: "/placeholder.svg?height=40&width=40&query=avatar",
          department: "管理层",
          lastActive: new Date().toISOString(),
        },
        "<EMAIL>": {
          id: "2",
          name: "销售经理",
          email: "<EMAIL>",
          role: "sales",
          avatar: "/placeholder.svg?height=40&width=40&query=avatar",
          department: "销售部",
          lastActive: new Date().toISOString(),
        },
        "<EMAIL>": {
          id: "3",
          name: "运维专员",
          email: "<EMAIL>",
          role: "operations",
          avatar: "/placeholder.svg?height=40&width=40&query=avatar",
          department: "技术运维部",
          lastActive: new Date().toISOString(),
        },
        "<EMAIL>": {
          id: "4",
          name: "客户用户",
          email: "<EMAIL>",
          role: "customer",
          avatar: "/placeholder.svg?height=40&width=40&query=avatar",
          lastActive: new Date().toISOString(),
        },
      }

      const user = demoUsers[email]
      if (!user || password !== "password") {
        throw new Error("Invalid credentials")
      }

      setUser(user)
      localStorage.setItem("cellforge_user", JSON.stringify(user))
    } catch (error) {
      console.error("Login failed:", error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    setUser(null)
    localStorage.removeItem("cellforge_user")
  }

  return (
    <AuthContext.Provider value={{ user, isLoading, permissions, hasPermission, login, logout }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
