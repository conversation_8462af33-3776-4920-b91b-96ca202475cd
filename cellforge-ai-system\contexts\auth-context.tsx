"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import { authApi, type User as ApiUser, type LoginRequest, type RegisterRequest } from "@/lib/api"

export type UserRole = "super_admin" | "sales" | "customer" | "operations" | "guest"

export interface Permission {
  id: string
  name: string
  description: string
  roles: UserRole[]
}

// 使用API中定义的User类型，但添加一些前端特定的字段
export interface User extends Omit<ApiUser, 'id'> {
  id: string // 前端使用string类型的ID
  name: string // 组合first_name和last_name
  avatar?: string
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  permissions: Permission[]
  hasPermission: (permissionId: string) => boolean
  login: (email: string, password: string) => Promise<void>
  register: (userData: RegisterRequest) => Promise<void>
  logout: () => void
  refreshUser: () => Promise<void>
}

const defaultPermissions: Permission[] = [
  {
    id: "view_dashboard",
    name: "查看仪表盘",
    description: "允许用户查看数据分析仪表盘",
    roles: ["super_admin", "sales"],
  },
  {
    id: "manage_customers",
    name: "管理客户",
    description: "允许用户添加、编辑和删除客户信息",
    roles: ["super_admin", "sales"],
  },
  {
    id: "view_customers",
    name: "查看客户",
    description: "允许用户查看客户信息",
    roles: ["super_admin", "sales", "operations"],
  },
  {
    id: "manage_knowledge",
    name: "管理知识库",
    description: "允许用户添加、编辑和删除知识库内容",
    roles: ["super_admin", "operations"],
  },
  {
    id: "view_knowledge",
    name: "查看知识库",
    description: "允许用户查看知识库内容",
    roles: ["super_admin", "sales", "operations", "customer"],
  },
  {
    id: "manage_solutions",
    name: "管理解决方案",
    description: "允许用户创建和编辑解决方案",
    roles: ["super_admin", "sales", "operations"],
  },
  {
    id: "view_solutions",
    name: "查看解决方案",
    description: "允许用户查看解决方案",
    roles: ["super_admin", "sales", "operations", "customer"],
  },
  {
    id: "manage_users",
    name: "管理用户",
    description: "允许用户添加、编辑和删除用户账户",
    roles: ["super_admin"],
  },
  {
    id: "manage_permissions",
    name: "管理权限",
    description: "允许用户配置系统权限",
    roles: ["super_admin"],
  },
]

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// 辅助函数：将API用户转换为前端用户格式
function transformApiUser(apiUser: ApiUser): User {
  return {
    ...apiUser,
    id: apiUser.id.toString(),
    name: [apiUser.first_name, apiUser.last_name].filter(Boolean).join(' ') || apiUser.username,
    avatar: "/placeholder.svg?height=40&width=40&query=avatar",
  }
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [permissions, setPermissions] = useState<Permission[]>(defaultPermissions)

  // 从API加载用户信息
  const loadUser = async () => {
    try {
      const apiUser = await authApi.getCurrentUser()
      const transformedUser = transformApiUser(apiUser)
      setUser(transformedUser)
    } catch (error) {
      console.error("Failed to load user:", error)
      // 如果获取用户失败，清除可能过期的token
      authApi.logout()
      setUser(null)
    }
  }

  useEffect(() => {
    const initAuth = async () => {
      setIsLoading(true)
      try {
        await loadUser()
      } catch (error) {
        console.error("Auth initialization failed:", error)
      } finally {
        setIsLoading(false)
      }
    }

    initAuth()
  }, [])

  const hasPermission = (permissionId: string) => {
    if (!user) return false
    const permission = permissions.find((p) => p.id === permissionId)
    return permission ? permission.roles.includes(user.role) : false
  }

  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      // 调用登录API
      await authApi.login({ email, password })

      // 登录成功后获取用户信息
      await loadUser()
    } catch (error) {
      console.error("Login failed:", error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (userData: RegisterRequest) => {
    setIsLoading(true)
    try {
      await authApi.register(userData)
      // 注册成功，但不自动登录，需要等待管理员审核
    } catch (error) {
      console.error("Registration failed:", error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const refreshUser = async () => {
    await loadUser()
  }

  const logout = () => {
    authApi.logout()
    setUser(null)
  }

  return (
    <AuthContext.Provider value={{
      user,
      isLoading,
      permissions,
      hasPermission,
      login,
      register,
      logout,
      refreshUser
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
