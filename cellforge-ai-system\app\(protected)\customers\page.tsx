import { hasPermission } from "@/lib/auth"
import { CustomerDashboard } from "./customer-dashboard"
import { AccessDenied } from "@/components/access-denied"

export default function CustomersPage() {
  // Update the return statement to conditionally render
  return (
    <div className="min-h-[calc(100vh-64px)]">
      {hasPermission("view_customers") ? <CustomerDashboard /> : <AccessDenied />}
    </div>
  )
}
