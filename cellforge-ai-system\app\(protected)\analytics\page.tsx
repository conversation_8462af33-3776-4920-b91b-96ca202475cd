import { hasPermission } from "@/lib/auth"
import { AnalyticsDashboard } from "./components/analytics-dashboard"
import { AccessDenied } from "@/components/access-denied"

const AnalyticsPage = async () => {
  const canViewDashboard = await hasPermission("view_dashboard")

  if (!canViewDashboard) {
    return (
      <div className="min-h-[calc(100vh-64px)]">
        <AccessDenied />
      </div>
    )
  }

  return (
    <div className="min-h-[calc(100vh-64px)]">
      <AnalyticsDashboard />
    </div>
  )
}

export default AnalyticsPage
