from typing import Dict, List, Optional, AsyncGenerator
from datetime import datetime
import openai
from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage, AIMessage
from langchain.prompts import ChatPromptTemplate
import asyncio

from app.core.config import settings


class AIResponse:
    def __init__(self, content: str, confidence: float, sources: List[str] = None, suggestions: List[str] = None):
        self.content = content
        self.confidence = confidence
        self.sources = sources or []
        self.suggestions = suggestions or []


class AIService:
    def __init__(self):
        # 检查是否有API密钥
        if settings.OPENAI_API_KEY:
            self.client = ChatOpenAI(
                model=settings.OPENAI_MODEL,
                temperature=0.3,
                api_key=settings.OPENAI_API_KEY,
                base_url=settings.OPENAI_API_BASE  # 使用DeepSeek API地址
            )
            self.use_real_ai = True
            print(f"使用AI服务: {settings.OPENAI_MODEL} @ {settings.OPENAI_API_BASE}")
        else:
            # 使用模拟AI服务
            self.client = None
            self.use_real_ai = False
            print("警告: 未配置API密钥，使用模拟AI响应")

        # 单细胞测序专业提示词模板
        self.system_prompt = """
        你是 CellForge AI，一个专业的单细胞测序方案咨询专家。你具备以下专业知识：

        1. 单细胞 RNA 测序 (scRNA-seq) 技术和流程
        2. 单细胞 ATAC 测序 (scATAC-seq) 技术
        3. 多组学单细胞测序 (multiome) 技术
        4. 样本制备、文库构建、测序平台选择
        5. 数据分析流程和生物信息学工具
        6. 成本预算和项目时间规划

        请根据用户的具体需求，提供专业、准确、实用的建议。始终使用中文回复。

        用户背景信息：
        - 机构：{organization}
        - 专业领域：{expertise}
        - 研究兴趣：{research_interests}

        相关知识库内容：
        {relevant_knowledge}

        对话历史：
        {conversation_history}
        """

    async def generate_response(
        self,
        message: str,
        context: Dict,
        conversation_type: str = "general"
    ) -> AIResponse:
        """
        生成 AI 回复
        """
        try:
            if self.use_real_ai and self.client:
                # 使用真实AI服务
                # 构建提示词
                system_message = self._build_system_message(context)

                # 构建消息列表
                messages = [SystemMessage(content=system_message)]

                # 添加对话历史
                if context.get("conversation_history"):
                    for msg in context["conversation_history"][-5:]:  # 只保留最近5轮对话
                        if msg["role"] == "user":
                            messages.append(HumanMessage(content=msg["content"]))
                        else:
                            messages.append(AIMessage(content=msg["content"]))

                # 添加当前用户消息
                messages.append(HumanMessage(content=message))

                # 调用 AI 模型
                response = await self.client.ainvoke(messages)
                ai_content = response.content
            else:
                # 使用模拟AI响应
                ai_content = self._generate_mock_response(message, context)

            # 分析回复质量和置信度
            confidence = self._calculate_confidence(ai_content, context)

            # 提取相关来源
            sources = self._extract_sources(context.get("relevant_knowledge", []))

            # 生成建议
            suggestions = await self._generate_suggestions(message, ai_content)

            return AIResponse(
                content=ai_content,
                confidence=confidence,
                sources=sources,
                suggestions=suggestions
            )

        except Exception as e:
            return AIResponse(
                content=f"抱歉，处理您的请求时出现错误：{str(e)}",
                confidence=0.0
            )

    async def generate_streaming_response(
        self,
        message: str,
        context: Dict
    ) -> AsyncGenerator[str, None]:
        """
        生成流式回复
        """
        try:
            system_message = self._build_system_message(context)

            messages = [
                {"role": "system", "content": system_message},
                {"role": "user", "content": message}
            ]

            # 使用 OpenAI 的流式 API
            stream = await openai.ChatCompletion.acreate(
                model=settings.OPENAI_MODEL,
                messages=messages,
                stream=True,
                temperature=0.3
            )

            async for chunk in stream:
                if chunk.choices[0].delta.get("content"):
                    yield chunk.choices[0].delta["content"]

        except Exception as e:
            yield f"流式响应错误：{str(e)}"

    def _build_system_message(self, context: Dict) -> str:
        """
        构建系统提示词
        """
        user_profile = context.get("user_profile", {})
        relevant_knowledge = context.get("relevant_knowledge", [])
        conversation_history = context.get("conversation_history", [])

        # 格式化知识库内容
        knowledge_text = "\n".join([
            f"- {item.get('title', '')}: {item.get('content', '')}"
            for item in relevant_knowledge[:3]  # 只包含前3个最相关的
        ])

        # 格式化对话历史
        history_text = "\n".join([
            f"{msg['role']}: {msg['content']}"
            for msg in conversation_history[-3:]  # 只包含最近3轮
        ])

        return self.system_prompt.format(
            organization=user_profile.get("organization", "未知"),
            expertise=user_profile.get("expertise", "未指定"),
            research_interests=user_profile.get("research_interests", "未指定"),
            relevant_knowledge=knowledge_text,
            conversation_history=history_text
        )

    def _calculate_confidence(self, response: str, context: Dict) -> float:
        """
        计算回复的置信度
        """
        # 简单的置信度计算逻辑
        confidence = 0.8  # 基础置信度

        # 如果有相关知识库支撑，提高置信度
        if context.get("relevant_knowledge"):
            confidence += 0.1

        # 如果回复包含具体的技术细节，提高置信度
        technical_keywords = ["测序", "文库", "细胞", "基因", "表达", "分析"]
        keyword_count = sum(1 for keyword in technical_keywords if keyword in response)
        confidence += keyword_count * 0.02

        return min(confidence, 1.0)

    def _extract_sources(self, knowledge_items: List[Dict]) -> List[str]:
        """
        提取回复的来源
        """
        return [
            item.get("source", item.get("title", "知识库"))
            for item in knowledge_items[:3]
        ]

    async def _generate_suggestions(self, user_message: str, ai_response: str) -> List[str]:
        """
        基于对话内容生成相关建议
        """
        suggestions = []

        # 基于关键词生成建议
        if "成本" in user_message or "费用" in user_message:
            suggestions.append("查看详细的成本预算分析")
            suggestions.append("比较不同测序平台的性价比")

        if "时间" in user_message or "周期" in user_message:
            suggestions.append("了解项目时间规划建议")
            suggestions.append("查看加急处理方案")

        if "样本" in user_message:
            suggestions.append("获取样本制备最佳实践")
            suggestions.append("了解样本质量控制要点")

        return suggestions[:3]  # 最多返回3个建议

    def _generate_mock_response(self, message: str, context: Dict) -> str:
        """
        生成模拟AI响应（当没有配置真实API时使用）
        """
        user_profile = context.get("user_profile", {})
        organization = user_profile.get("organization", "您的机构")

        # 基于关键词生成不同的响应
        if any(keyword in message.lower() for keyword in ["单细胞", "scrna", "测序"]):
            return f"""感谢您咨询单细胞测序方案！

基于您的需求，我为{organization}推荐以下单细胞RNA测序方案：

**技术平台推荐：**
- 10x Genomics Chromium：适合大规模细胞分析，通量高
- Smart-seq3：适合需要全长转录本信息的研究
- Drop-seq：成本效益较高的选择

**样本制备要点：**
1. 确保细胞活力 >80%
2. 细胞浓度控制在 500-1000 cells/μL
3. 避免细胞聚集和碎片

**预估成本：**
- 10x Genomics: 约 ¥800-1200/样本
- Smart-seq3: 约 ¥1500-2000/样本

**项目周期：**
- 样本制备：1-2天
- 文库构建：2-3天
- 测序分析：5-7天

如需更详细的方案设计，请提供具体的研究目标和样本信息。

*注：当前为演示模式，实际使用时将连接专业AI模型提供更精准的建议。*"""

        elif any(keyword in message.lower() for keyword in ["成本", "费用", "价格"]):
            return f"""关于单细胞测序的成本分析：

**主要费用构成：**
1. **样本制备费用：** ¥200-500/样本
2. **文库构建费用：** ¥400-800/样本
3. **测序费用：** ¥300-600/样本
4. **数据分析费用：** ¥200-400/样本

**不同平台成本对比：**
- **10x Genomics：** ¥800-1200/样本（推荐）
- **Smart-seq3：** ¥1500-2000/样本
- **Drop-seq：** ¥600-900/样本

**成本优化建议：**
- 批量处理可降低10-15%成本
- 选择合适的测序深度
- 考虑多样本复用策略

为{organization}制定具体预算方案，请告知样本数量和研究需求。"""

        elif any(keyword in message.lower() for keyword in ["时间", "周期", "多久"]):
            return f"""单细胞测序项目时间规划：

**标准流程时间：**
1. **项目咨询：** 1-2天
2. **样本制备：** 1-3天
3. **质量检测：** 1天
4. **文库构建：** 2-4天
5. **测序运行：** 3-5天
6. **数据分析：** 5-10天
7. **报告交付：** 2-3天

**总计：** 15-28天（标准流程）

**加急服务：**
- 可缩短至10-15天
- 需额外支付20-30%费用

**影响因素：**
- 样本数量和复杂度
- 测序深度要求
- 分析复杂程度

为{organization}制定详细时间计划，请提供具体项目需求。"""

        else:
            return f"""您好！我是CellForge AI单细胞测序咨询专家。

我可以为{organization}提供以下服务：

🔬 **技术咨询**
- 单细胞RNA测序方案设计
- 样本制备指导
- 平台选择建议

💰 **成本分析**
- 详细费用预算
- 性价比分析
- 优化建议

⏰ **项目规划**
- 时间安排
- 流程优化
- 质量控制

📊 **数据分析**
- 分析流程设计
- 结果解读
- 可视化方案

请告诉我您的具体需求，比如：
- 研究目标和样本类型
- 预期细胞数量
- 预算范围
- 时间要求

我将为您提供专业的个性化建议！

*注：当前为演示模式，配置API密钥后将提供更智能的专业咨询。*"""