from typing import Dict, List, Optional, AsyncGenerator
from datetime import datetime
import openai
from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage, AIMessage
from langchain.prompts import ChatPromptTemplate
import asyncio

from app.core.config import settings


class AIResponse:
    def __init__(self, content: str, confidence: float, sources: List[str] = None, suggestions: List[str] = None):
        self.content = content
        self.confidence = confidence
        self.sources = sources or []
        self.suggestions = suggestions or []


class AIService:
    def __init__(self):
        self.client = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=0.3,
            api_key=settings.OPENAI_API_KEY
        )
        
        # 单细胞测序专业提示词模板
        self.system_prompt = """
        你是 CellForge AI，一个专业的单细胞测序方案咨询专家。你具备以下专业知识：

        1. 单细胞 RNA 测序 (scRNA-seq) 技术和流程
        2. 单细胞 ATAC 测序 (scATAC-seq) 技术
        3. 多组学单细胞测序 (multiome) 技术
        4. 样本制备、文库构建、测序平台选择
        5. 数据分析流程和生物信息学工具
        6. 成本预算和项目时间规划

        请根据用户的具体需求，提供专业、准确、实用的建议。始终使用中文回复。

        用户背景信息：
        - 机构：{organization}
        - 专业领域：{expertise}
        - 研究兴趣：{research_interests}

        相关知识库内容：
        {relevant_knowledge}

        对话历史：
        {conversation_history}
        """

    async def generate_response(
        self, 
        message: str, 
        context: Dict, 
        conversation_type: str = "general"
    ) -> AIResponse:
        """
        生成 AI 回复
        """
        try:
            # 构建提示词
            system_message = self._build_system_message(context)
            
            # 构建消息列表
            messages = [SystemMessage(content=system_message)]
            
            # 添加对话历史
            if context.get("conversation_history"):
                for msg in context["conversation_history"][-5:]:  # 只保留最近5轮对话
                    if msg["role"] == "user":
                        messages.append(HumanMessage(content=msg["content"]))
                    else:
                        messages.append(AIMessage(content=msg["content"]))
            
            # 添加当前用户消息
            messages.append(HumanMessage(content=message))
            
            # 调用 AI 模型
            response = await self.client.ainvoke(messages)
            
            # 分析回复质量和置信度
            confidence = self._calculate_confidence(response.content, context)
            
            # 提取相关来源
            sources = self._extract_sources(context.get("relevant_knowledge", []))
            
            # 生成建议
            suggestions = await self._generate_suggestions(message, response.content)
            
            return AIResponse(
                content=response.content,
                confidence=confidence,
                sources=sources,
                suggestions=suggestions
            )
            
        except Exception as e:
            return AIResponse(
                content=f"抱歉，处理您的请求时出现错误：{str(e)}",
                confidence=0.0
            )

    async def generate_streaming_response(
        self, 
        message: str, 
        context: Dict
    ) -> AsyncGenerator[str, None]:
        """
        生成流式回复
        """
        try:
            system_message = self._build_system_message(context)
            
            messages = [
                {"role": "system", "content": system_message},
                {"role": "user", "content": message}
            ]
            
            # 使用 OpenAI 的流式 API
            stream = await openai.ChatCompletion.acreate(
                model=settings.OPENAI_MODEL,
                messages=messages,
                stream=True,
                temperature=0.3
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.get("content"):
                    yield chunk.choices[0].delta["content"]
                    
        except Exception as e:
            yield f"流式响应错误：{str(e)}"

    def _build_system_message(self, context: Dict) -> str:
        """
        构建系统提示词
        """
        user_profile = context.get("user_profile", {})
        relevant_knowledge = context.get("relevant_knowledge", [])
        conversation_history = context.get("conversation_history", [])
        
        # 格式化知识库内容
        knowledge_text = "\n".join([
            f"- {item.get('title', '')}: {item.get('content', '')}"
            for item in relevant_knowledge[:3]  # 只包含前3个最相关的
        ])
        
        # 格式化对话历史
        history_text = "\n".join([
            f"{msg['role']}: {msg['content']}"
            for msg in conversation_history[-3:]  # 只包含最近3轮
        ])
        
        return self.system_prompt.format(
            organization=user_profile.get("organization", "未知"),
            expertise=user_profile.get("expertise", "未指定"),
            research_interests=user_profile.get("research_interests", "未指定"),
            relevant_knowledge=knowledge_text,
            conversation_history=history_text
        )

    def _calculate_confidence(self, response: str, context: Dict) -> float:
        """
        计算回复的置信度
        """
        # 简单的置信度计算逻辑
        confidence = 0.8  # 基础置信度
        
        # 如果有相关知识库支撑，提高置信度
        if context.get("relevant_knowledge"):
            confidence += 0.1
        
        # 如果回复包含具体的技术细节，提高置信度
        technical_keywords = ["测序", "文库", "细胞", "基因", "表达", "分析"]
        keyword_count = sum(1 for keyword in technical_keywords if keyword in response)
        confidence += keyword_count * 0.02
        
        return min(confidence, 1.0)

    def _extract_sources(self, knowledge_items: List[Dict]) -> List[str]:
        """
        提取回复的来源
        """
        return [
            item.get("source", item.get("title", "知识库"))
            for item in knowledge_items[:3]
        ]

    async def _generate_suggestions(self, user_message: str, ai_response: str) -> List[str]:
        """
        基于对话内容生成相关建议
        """
        suggestions = []
        
        # 基于关键词生成建议
        if "成本" in user_message or "费用" in user_message:
            suggestions.append("查看详细的成本预算分析")
            suggestions.append("比较不同测序平台的性价比")
        
        if "时间" in user_message or "周期" in user_message:
            suggestions.append("了解项目时间规划建议")
            suggestions.append("查看加急处理方案")
        
        if "样本" in user_message:
            suggestions.append("获取样本制备最佳实践")
            suggestions.append("了解样本质量控制要点")
        
        return suggestions[:3]  # 最多返回3个建议 