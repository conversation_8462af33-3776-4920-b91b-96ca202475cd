"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { UserRole } from "@/contexts/auth-context"
import { Plus, Edit2 } from "lucide-react"

interface RoleDefinition {
  id: UserRole
  name: string
  description: string
  userCount: number
}

export function RoleManagement() {
  const [roles, setRoles] = useState<RoleDefinition[]>([
    {
      id: "super_admin",
      name: "超级管理员",
      description: "拥有系统所有权限，可以管理用户和权限",
      userCount: 1,
    },
    {
      id: "sales",
      name: "销售人员",
      description: "可以管理客户和方案，查看分析数据",
      userCount: 5,
    },
    {
      id: "operations",
      name: "运维人员",
      description: "负责知识库维护和系统运营",
      userCount: 3,
    },
    {
      id: "customer",
      name: "客户",
      description: "可以查看方案和知识库内容",
      userCount: 25,
    },
  ])

  const [editingRole, setEditingRole] = useState<RoleDefinition | null>(null)
  const [newUserRole, setNewUserRole] = useState<UserRole | "">("")

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>角色管理</CardTitle>
        <Dialog>
          <DialogTrigger asChild>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              添加角色
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>添加新角色</DialogTitle>
              <DialogDescription>创建新的用户角色并设置权限</DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="role-name">角色名称</Label>
                <Input id="role-name" placeholder="输入角色名称" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="role-description">角色描述</Label>
                <Input id="role-description" placeholder="描述该角色的权限和职责" />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline">取消</Button>
              <Button>创建角色</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {roles.map((role) => (
            <div key={role.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <div className="flex items-center space-x-2">
                  <h3 className="font-medium">{role.name}</h3>
                  <Badge variant="outline">{role.userCount} 用户</Badge>
                </div>
                <p className="text-sm text-slate-500 mt-1">{role.description}</p>
              </div>
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="sm" onClick={() => setEditingRole(role)}>
                    <Edit2 className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>编辑角色</DialogTitle>
                    <DialogDescription>修改角色信息和权限</DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="edit-role-name">角色名称</Label>
                      <Input id="edit-role-name" defaultValue={editingRole?.name} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-role-description">角色描述</Label>
                      <Input id="edit-role-description" defaultValue={editingRole?.description} />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline">取消</Button>
                    <Button>保存更改</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          ))}
        </div>

        <div className="mt-6 border-t pt-6">
          <h3 className="font-medium mb-4">分配用户角色</h3>
          <div className="flex space-x-4">
            <div className="flex-1">
              <Label htmlFor="user-email" className="mb-2 block">
                用户邮箱
              </Label>
              <Input id="user-email" placeholder="输入用户邮箱" />
            </div>
            <div className="w-48">
              <Label htmlFor="user-role" className="mb-2 block">
                角色
              </Label>
              <Select value={newUserRole} onValueChange={(value) => setNewUserRole(value as UserRole)}>
                <SelectTrigger id="user-role">
                  <SelectValue placeholder="选择角色" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button>分配</Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
