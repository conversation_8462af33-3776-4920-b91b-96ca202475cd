# CellForge AI Backend

## 项目概述

CellForge AI Backend 是一个专为单细胞测序方案咨询设计的智能后端系统。该系统为前端 React/Next.js 应用提供强大的 API 服务，包括智能对话、知识库管理、客户画像分析、方案生成和数据分析等功能。

## 核心功能

### 🤖 智能对话系统
- **专业 AI 咨询**: 基于 GPT-4 的单细胞测序专业咨询
- **实时对话**: WebSocket 支持的流式对话体验
- **上下文理解**: 结合用户画像和历史对话的智能回复
- **知识库增强**: RAG (检索增强生成) 技术提升回复准确性

### 📚 知识库管理
- **向量化存储**: 基于 ChromaDB 的语义搜索
- **多源整合**: 支持文档、论文、协议等多种知识源
- **智能检索**: 语义匹配的相关内容推荐
- **实时更新**: 支持知识库的动态更新和版本控制

### 👥 客户画像系统
- **多维分析**: 研究背景、技术需求、预算范围等
- **行为追踪**: 对话记录、偏好分析、决策模式
- **个性化推荐**: 基于画像的定制化方案建议
- **潜在客户识别**: 机器学习驱动的客户价值评估

### 🔬 方案生成引擎
- **智能配置**: 基于需求自动生成测序方案
- **成本估算**: 精确的费用预算和时间规划
- **方案比较**: 多种方案的优劣势对比分析
- **可视化展示**: 方案流程图和参数配置图表

### 📊 数据分析平台
- **业务指标**: 咨询量、转化率、客户满意度等
- **技术指标**: AI 回复质量、知识库命中率等
- **用户洞察**: 用户行为模式和需求趋势分析
- **实时监控**: 系统性能和服务质量监控

## 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   Backend       │
│   (Next.js)     │◄───┤   (FastAPI)     │◄───┤   Services      │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▲                       │
                                │                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vector DB     │    │   Cache Layer   │    │   Main Database │
│   (ChromaDB)    │    │   (Redis)       │    │   (PostgreSQL)  │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心技术栈
- **框架**: FastAPI (Python) - 高性能异步 Web 框架
- **数据库**: PostgreSQL (主库) + Redis (缓存) + ChromaDB (向量库)
- **AI/ML**: OpenAI GPT-4, LangChain, Sentence Transformers
- **认证**: JWT + OAuth 2.0
- **部署**: Docker + Kubernetes
- **监控**: Prometheus + Grafana

## 项目结构

```
cellforge-backend/
├── app/
│   ├── api/                    # API 路由
│   │   ├── endpoints/         # 具体端点实现
│   │   │   ├── auth.py       # 认证相关
│   │   │   ├── conversation.py  # 智能对话
│   │   │   ├── knowledge.py     # 知识库管理
│   │   │   ├── customer.py      # 客户画像
│   │   │   ├── solution.py      # 方案生成
│   │   │   └── analytics.py     # 数据分析
│   │   └── router.py          # 路由汇总
│   ├── core/                  # 核心配置
│   │   ├── config.py         # 应用配置
│   │   ├── database.py       # 数据库连接
│   │   ├── auth.py           # 认证逻辑
│   │   └── security.py       # 安全配置
│   ├── models/                # 数据模型
│   │   ├── user.py           # 用户模型
│   │   ├── conversation.py   # 对话模型
│   │   ├── knowledge.py      # 知识库模型
│   │   └── solution.py       # 方案模型
│   ├── schemas/               # Pydantic 模式
│   │   ├── user.py           # 用户模式
│   │   ├── conversation.py   # 对话模式
│   │   └── common.py         # 通用模式
│   ├── services/              # 业务逻辑
│   │   ├── ai_service.py     # AI 服务
│   │   ├── knowledge_service.py  # 知识库服务
│   │   ├── customer_service.py   # 客户服务
│   │   └── analytics_service.py  # 分析服务
│   ├── utils/                 # 工具函数
│   │   ├── file_processor.py # 文件处理
│   │   ├── email_sender.py   # 邮件发送
│   │   └── data_validator.py # 数据验证
│   └── main.py               # 应用入口
├── migrations/                # 数据库迁移
├── tests/                     # 测试文件
├── scripts/                   # 脚本文件
├── docker-compose.yml         # Docker 编排
├── Dockerfile                 # Docker 镜像
├── requirements.txt           # Python 依赖
└── README.md                  # 项目文档
```

## 快速开始

### 环境要求
- Python 3.11+
- PostgreSQL 14+
- Redis 6+
- Docker (可选)

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd cellforge-backend
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **环境配置**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和 API 密钥
```

4. **数据库初始化**
```bash
alembic upgrade head
```

5. **启动服务**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Docker 部署

```bash
docker-compose up -d
```

## API 文档

启动服务后，访问以下地址查看 API 文档：
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 主要 API 端点

### 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/refresh` - 刷新令牌

### 智能对话
- `POST /api/v1/conversation/chat` - AI 对话
- `WS /api/v1/conversation/ws/{client_id}` - 实时对话
- `GET /api/v1/conversation/history` - 对话历史

### 知识库管理
- `POST /api/v1/knowledge/upload` - 上传知识文档
- `GET /api/v1/knowledge/search` - 搜索知识库
- `PUT /api/v1/knowledge/{id}` - 更新知识条目

### 客户画像
- `GET /api/v1/customer/profile/{user_id}` - 获取客户画像
- `POST /api/v1/customer/analyze` - 分析客户行为
- `GET /api/v1/customer/insights` - 客户洞察报告

### 方案生成
- `POST /api/v1/solution/generate` - 生成测序方案
- `GET /api/v1/solution/compare` - 方案对比
- `POST /api/v1/solution/optimize` - 方案优化

### 数据分析
- `GET /api/v1/analytics/dashboard` - 仪表板数据
- `GET /api/v1/analytics/reports` - 分析报告
- `GET /api/v1/analytics/metrics` - 业务指标

## 与前端集成

### 认证集成
后端使用 JWT 认证，与前端 NextAuth 无缝集成：

```typescript
// 前端 API 调用示例
const response = await fetch('/api/v1/conversation/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    message: "我想了解单细胞 RNA 测序的基本流程",
    history: []
  })
})
```

### WebSocket 连接
```typescript
// 实时对话连接
const ws = new WebSocket(`ws://localhost:8000/api/v1/conversation/ws/${userId}`)
ws.onmessage = (event) => {
  const data = JSON.parse(event.data)
  if (data.type === 'chunk') {
    // 处理流式响应
  }
}
```

## 性能优化

### 缓存策略
- **Redis 缓存**: 用户会话、频繁查询结果
- **向量缓存**: 知识库检索结果缓存
- **应用缓存**: AI 回复缓存机制

### 异步处理
- **异步 AI 调用**: 非阻塞的 AI 服务调用
- **后台任务**: Celery 处理耗时任务
- **流式响应**: WebSocket 实时数据传输

### 数据库优化
- **连接池**: 异步数据库连接池
- **索引优化**: 核心查询字段索引
- **分页查询**: 大数据集分页处理

## 安全考虑

### 认证授权
- JWT 令牌认证
- 角色基础访问控制 (RBAC)
- API 密钥管理

### 数据安全
- 密码加密存储
- 敏感数据脱敏
- HTTPS 强制加密

### API 安全
- 请求频率限制
- CORS 策略配置
- SQL 注入防护

## 监控与日志

### 应用监控
- 性能指标监控
- 错误率追踪
- 服务健康检查

### 业务监控
- API 调用统计
- 用户行为分析
- AI 服务质量监控

## 部署指南

### 生产环境部署
1. 使用 Docker 容器化部署
2. Kubernetes 集群管理
3. 负载均衡配置
4. 数据库主从复制
5. Redis 集群部署

### 环境变量配置
```bash
# .env 生产环境配置
DATABASE_URL=******************************/cellforge_prod
REDIS_URL=redis://redis:6379/0
OPENAI_API_KEY=your_openai_key
SECRET_KEY=your_secret_key
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启 Pull Request

## 许可证

此项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者: CellForge AI Team
- 邮箱: <EMAIL>
- 文档: https://docs.cellforge-ai.com
- 支持: https://support.cellforge-ai.com 