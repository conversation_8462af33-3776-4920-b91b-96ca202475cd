#!/usr/bin/env python3
"""
简化的数据库初始化脚本
只创建用户表，暂时不创建复杂的关系表
"""
import sqlite3
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.security import get_password_hash

def init_simple_database():
    """初始化简单的SQLite数据库"""
    
    # 数据库文件路径
    db_path = "cellforge.db"
    
    # 连接数据库（如果不存在会自动创建）
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 创建用户表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email VARCHAR(255) UNIQUE NOT NULL,
                username VARCHAR(50) UNIQUE NOT NULL,
                hashed_password VARCHAR(255) NOT NULL,
                first_name VA<PERSON><PERSON><PERSON>(100),
                last_name <PERSON><PERSON><PERSON><PERSON>(100),
                phone VARCHAR(20),
                organization VARCHAR(255),
                department VARCHAR(255),
                title VARCHAR(255),
                role VARCHAR(20) DEFAULT 'customer',
                status VARCHAR(20) DEFAULT 'active',
                is_verified BOOLEAN DEFAULT 1,
                preferred_language VARCHAR(10) DEFAULT 'zh',
                email_notifications BOOLEAN DEFAULT 1,
                research_interests TEXT,
                expertise_areas TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')
        
        # 创建简单的对话表（可选）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                title VARCHAR(255),
                conversation_type VARCHAR(50) DEFAULT 'single_cell_consulting',
                status VARCHAR(20) DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP,
                last_message_at TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # 创建消息表（可选）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                conversation_id INTEGER NOT NULL,
                role VARCHAR(20) NOT NULL,
                content TEXT NOT NULL,
                message_metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (conversation_id) REFERENCES conversations (id)
            )
        ''')
        
        # 检查是否已有测试用户
        cursor.execute("SELECT COUNT(*) FROM users WHERE email = ?", ("<EMAIL>",))
        if cursor.fetchone()[0] == 0:
            # 创建测试用户
            test_users = [
                {
                    "email": "<EMAIL>",
                    "username": "testuser",
                    "password": "Test123456",
                    "first_name": "Test",
                    "last_name": "User",
                    "organization": "CellForge AI",
                    "role": "customer"
                },
                {
                    "email": "<EMAIL>",
                    "username": "admin",
                    "password": "Admin123456",
                    "first_name": "Admin",
                    "last_name": "User",
                    "organization": "CellForge AI",
                    "role": "super_admin"
                },
                {
                    "email": "<EMAIL>",
                    "username": "sales",
                    "password": "Sales123456",
                    "first_name": "Sales",
                    "last_name": "Manager",
                    "organization": "CellForge AI",
                    "role": "sales"
                }
            ]
            
            for user_data in test_users:
                hashed_password = get_password_hash(user_data["password"])
                cursor.execute('''
                    INSERT INTO users (
                        email, username, hashed_password, first_name, last_name,
                        organization, role, status, is_verified
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    user_data["email"],
                    user_data["username"],
                    hashed_password,
                    user_data["first_name"],
                    user_data["last_name"],
                    user_data["organization"],
                    user_data["role"],
                    "active",
                    1
                ))
                print(f"创建用户: {user_data['email']}")
        else:
            print("测试用户已存在")
        
        # 提交更改
        conn.commit()
        print("数据库初始化成功！")
        
        print("\n可用的测试账户:")
        print("1. <EMAIL> / Test123456 (客户)")
        print("2. <EMAIL> / Admin123456 (超级管理员)")
        print("3. <EMAIL> / Sales123456 (销售)")
        
        # 显示数据库信息
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"\n数据库表: {[table[0] for table in tables]}")
        
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"用户数量: {user_count}")
        
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    init_simple_database()
