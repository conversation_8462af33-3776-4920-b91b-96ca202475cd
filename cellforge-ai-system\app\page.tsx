"use client"
import { ConversationInterface } from "@/components/conversation-interface"
import { CustomerDashboard } from "@/components/customer-dashboard"
import { SolutionPresentation } from "@/components/solution-presentation"
import { KnowledgeManagement } from "@/components/knowledge-management"
import { AnalyticsDashboard } from "@/components/analytics-dashboard"
import { <PERSON><PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function CellForgeAI() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <header className="bg-white border-b border-slate-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">CF</span>
            </div>
            <h1 className="text-xl font-semibold text-slate-900">CellForge AI</h1>
          </div>
          <div className="text-sm text-slate-600">单细胞测序方案咨询系统</div>
        </div>
      </header>

      <Tabs defaultValue="conversation" className="w-full">
        <TabsList className="grid w-full grid-cols-5 bg-white border-b">
          <TabsTrigger value="conversation">智能对话</TabsTrigger>
          <TabsTrigger value="customer">客户画像</TabsTrigger>
          <TabsTrigger value="solution">方案展示</TabsTrigger>
          <TabsTrigger value="knowledge">知识库</TabsTrigger>
          <TabsTrigger value="analytics">数据分析</TabsTrigger>
        </TabsList>

        <TabsContent value="conversation" className="mt-0">
          <ConversationInterface />
        </TabsContent>

        <TabsContent value="customer" className="mt-0">
          <CustomerDashboard />
        </TabsContent>

        <TabsContent value="solution" className="mt-0">
          <SolutionPresentation />
        </TabsContent>

        <TabsContent value="knowledge" className="mt-0">
          <KnowledgeManagement />
        </TabsContent>

        <TabsContent value="analytics" className="mt-0">
          <AnalyticsDashboard />
        </TabsContent>
      </Tabs>
    </div>
  )
}
