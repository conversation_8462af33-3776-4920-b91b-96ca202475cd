"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { useAuth } from "@/contexts/auth-context"
import { PermissionGate } from "@/components/permission-gate"
import { Save, RefreshCw, Database, Shield, Users, Bell, Cog, Key } from "lucide-react"
import Link from "next/link"

export default function SettingsPage() {
  const { user } = useAuth()
  const [systemName, setSystemName] = useState("CellForge AI")
  const [systemLogo, setSystemLogo] = useState("/placeholder.svg")
  const [primaryColor, setPrimaryColor] = useState("#3b82f6")
  const [language, setLanguage] = useState("zh-CN")

  return (
    <div className="container py-10">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">系统设置</h1>
          <p className="text-muted-foreground">管理系统配置和偏好设置</p>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="bg-background border-b w-full justify-start rounded-none p-0 h-auto">
          <TabsTrigger
            value="general"
            className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background px-4 py-3 data-[state=active]:shadow-none"
          >
            <Cog className="h-4 w-4 mr-2" />
            基本设置
          </TabsTrigger>
          <PermissionGate permission="manage_users">
            <TabsTrigger
              value="users"
              className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background px-4 py-3 data-[state=active]:shadow-none"
              asChild
            >
              <Link href="/settings/users">
                <Users className="h-4 w-4 mr-2" />
                用户管理
              </Link>
            </TabsTrigger>
          </PermissionGate>
          <PermissionGate permission="manage_permissions">
            <TabsTrigger
              value="roles"
              className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background px-4 py-3 data-[state=active]:shadow-none"
              asChild
            >
              <Link href="/settings/roles">
                <Shield className="h-4 w-4 mr-2" />
                角色管理
              </Link>
            </TabsTrigger>
          </PermissionGate>
          <PermissionGate permission="manage_permissions">
            <TabsTrigger
              value="permissions"
              className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background px-4 py-3 data-[state=active]:shadow-none"
              asChild
            >
              <Link href="/settings/permissions">
                <Key className="h-4 w-4 mr-2" />
                权限设置
              </Link>
            </TabsTrigger>
          </PermissionGate>
          <PermissionGate permission="manage_integrations">
            <TabsTrigger
              value="integrations"
              className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background px-4 py-3 data-[state=active]:shadow-none"
            >
              <Database className="h-4 w-4 mr-2" />
              系统集成
            </TabsTrigger>
          </PermissionGate>
          <TabsTrigger
            value="notifications"
            className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background px-4 py-3 data-[state=active]:shadow-none"
          >
            <Bell className="h-4 w-4 mr-2" />
            通知设置
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>系统信息</CardTitle>
              <CardDescription>配置系统的基本信息和外观</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="system-name">系统名称</Label>
                  <Input id="system-name" value={systemName} onChange={(e) => setSystemName(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="system-logo">系统Logo</Label>
                  <div className="flex items-center gap-4">
                    <img
                      src={systemLogo || "/placeholder.svg"}
                      alt="System Logo"
                      className="w-10 h-10 rounded-md border"
                    />
                    <Input id="system-logo" type="file" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="primary-color">主题色</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="primary-color"
                      type="color"
                      value={primaryColor}
                      onChange={(e) => setPrimaryColor(e.target.value)}
                      className="w-12 h-10 p-1"
                    />
                    <Input value={primaryColor} onChange={(e) => setPrimaryColor(e.target.value)} className="flex-1" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="language">系统语言</Label>
                  <select
                    id="language"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={language}
                    onChange={(e) => setLanguage(e.target.value)}
                  >
                    <option value="zh-CN">简体中文</option>
                    <option value="en-US">English (US)</option>
                    <option value="ja-JP">日本語</option>
                  </select>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">系统功能</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-chat" className="flex-1">
                      启用智能对话
                      <p className="text-sm text-muted-foreground">允许用户使用AI对话功能</p>
                    </Label>
                    <Switch id="enable-chat" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-analytics" className="flex-1">
                      启用数据分析
                      <p className="text-sm text-muted-foreground">收集和分析系统使用数据</p>
                    </Label>
                    <Switch id="enable-analytics" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-knowledge" className="flex-1">
                      启用知识库
                      <p className="text-sm text-muted-foreground">允许用户访问和管理知识库</p>
                    </Label>
                    <Switch id="enable-knowledge" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-solutions" className="flex-1">
                      启用方案生成
                      <p className="text-sm text-muted-foreground">允许系统自动生成解决方案</p>
                    </Label>
                    <Switch id="enable-solutions" defaultChecked />
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button>
                  <Save className="h-4 w-4 mr-2" />
                  保存设置
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>系统维护</CardTitle>
              <CardDescription>系统维护和数据管理选项</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">数据备份</h3>
                  <p className="text-sm text-muted-foreground mb-4">创建系统数据的完整备份</p>
                  <Button variant="outline">创建备份</Button>
                </div>
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">缓存清理</h3>
                  <p className="text-sm text-muted-foreground mb-4">清理系统缓存以提高性能</p>
                  <Button variant="outline">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    清理缓存
                  </Button>
                </div>
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">系统日志</h3>
                  <p className="text-sm text-muted-foreground mb-4">查看系统操作日志</p>
                  <Button variant="outline">查看日志</Button>
                </div>
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">系统更新</h3>
                  <p className="text-sm text-muted-foreground mb-4">检查并安装系统更新</p>
                  <Button variant="outline">检查更新</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>API集成</CardTitle>
              <CardDescription>配置第三方API和服务集成</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">AI模型配置</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="openai-api-key">OpenAI API密钥</Label>
                    <Input id="openai-api-key" type="password" placeholder="sk-..." />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="openai-model">默认模型</Label>
                    <select
                      id="openai-model"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="gpt-4o">GPT-4o</option>
                      <option value="gpt-4">GPT-4</option>
                      <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    </select>
                  </div>
                </div>

                <Separator />

                <h3 className="text-lg font-medium">数据库集成</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="db-host">数据库主机</Label>
                    <Input id="db-host" placeholder="localhost" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="db-port">数据库端口</Label>
                    <Input id="db-port" placeholder="5432" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="db-name">数据库名称</Label>
                    <Input id="db-name" placeholder="cellforge" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="db-user">数据库用户</Label>
                    <Input id="db-user" placeholder="postgres" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="db-password">数据库密码</Label>
                    <Input id="db-password" type="password" placeholder="********" />
                  </div>
                </div>

                <Separator />

                <h3 className="text-lg font-medium">邮件服务配置</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="smtp-host">SMTP服务器</Label>
                    <Input id="smtp-host" placeholder="smtp.example.com" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="smtp-port">SMTP端口</Label>
                    <Input id="smtp-port" placeholder="587" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="smtp-user">SMTP用户名</Label>
                    <Input id="smtp-user" placeholder="<EMAIL>" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="smtp-password">SMTP密码</Label>
                    <Input id="smtp-password" type="password" placeholder="********" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email-from">发件人地址</Label>
                    <Input id="email-from" placeholder="<EMAIL>" />
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button>
                  <Save className="h-4 w-4 mr-2" />
                  保存集成设置
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>通知设置</CardTitle>
              <CardDescription>配置系统通知和提醒</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">电子邮件通知</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="email-new-user" className="flex-1">
                      新用户注册
                      <p className="text-sm text-muted-foreground">当新用户注册时发送通知</p>
                    </Label>
                    <Switch id="email-new-user" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="email-new-solution" className="flex-1">
                      新方案生成
                      <p className="text-sm text-muted-foreground">当系统生成新方案时发送通知</p>
                    </Label>
                    <Switch id="email-new-solution" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="email-system-alert" className="flex-1">
                      系统警报
                      <p className="text-sm text-muted-foreground">当系统出现异常时发送通知</p>
                    </Label>
                    <Switch id="email-system-alert" defaultChecked />
                  </div>
                </div>

                <Separator />

                <h3 className="text-lg font-medium">系统内通知</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="notify-new-message" className="flex-1">
                      新消息提醒
                      <p className="text-sm text-muted-foreground">当收到新消息时显示通知</p>
                    </Label>
                    <Switch id="notify-new-message" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="notify-task-complete" className="flex-1">
                      任务完成提醒
                      <p className="text-sm text-muted-foreground">当系统任务完成时显示通知</p>
                    </Label>
                    <Switch id="notify-task-complete" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="notify-updates" className="flex-1">
                      系统更新提醒
                      <p className="text-sm text-muted-foreground">当有系统更新时显示通知</p>
                    </Label>
                    <Switch id="notify-updates" defaultChecked />
                  </div>
                </div>

                <Separator />

                <h3 className="text-lg font-medium">通知频率</h3>
                <div className="space-y-2">
                  <Label htmlFor="notification-frequency">通知发送频率</Label>
                  <select
                    id="notification-frequency"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="immediately">立即发送</option>
                    <option value="hourly">每小时汇总</option>
                    <option value="daily">每日汇总</option>
                    <option value="weekly">每周汇总</option>
                  </select>
                </div>
              </div>

              <div className="flex justify-end">
                <Button>
                  <Save className="h-4 w-4 mr-2" />
                  保存通知设置
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
