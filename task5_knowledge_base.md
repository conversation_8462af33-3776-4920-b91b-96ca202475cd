# 任务5: 知识库系统开发

## 给Cursor/Augment的提示词

你是一个知识工程专家，需要为单细胞测序方案生成系统构建一个智能化的知识库管理系统。

### 系统目标
- 构建结构化的单细胞测序知识体系
- 实现智能的文档管理和检索
- 支持动态知识更新和版本控制
- 提供API接口供AI系统调用
- 确保知识的准确性和时效性

### 技术架构
- **数据存储**: MongoDB + Elasticsearch
- **向量检索**: Pinecone/Weaviate + OpenAI Embeddings
- **文档处理**: PDF解析、OCR、NLP预处理
- **知识图谱**: Neo4j存储实体关系
- **缓存系统**: Redis提升检索性能

### 核心模块开发

#### 1. 文档管理系统 (DocumentManager)
```typescript
interface Document {
  _id: string;
  title: string;
  content: string;
  metadata: {
    category: DocumentCategory;
    subcategory: string;
    version: string;
    language: string;
    publishDate: Date;
    lastUpdated: Date;
    source: string;
    authors: string[];
    tags: string[];
    keywords: string[];
  };
  structure: {
    sections: DocumentSection[];
    figures: Figure[];
    tables: Table[];
    references: Reference[];
  };
  processing: {
    embeddings: number[];
    extractedEntities: Entity[];
    relationships: Relationship[];
    summary: string;
  };
  access: {
    visibility: 'public' | 'internal' | 'restricted';
    permissions: string[];
    downloadCount: number;
    lastAccessed: Date;
  };
}

enum DocumentCategory {
  TECHNICAL_PROTOCOL = 'technical_protocol',
  PRODUCT_SPECIFICATION = 'product_specification',
  SCIENTIFIC_LITERATURE = 'scientific_literature',
  TROUBLESHOOTING_GUIDE = 'troubleshooting_guide',
  CASE_STUDY = 'case_study',
  REGULATORY_DOCUMENT = 'regulatory_document',
  TRAINING_MATERIAL = 'training_material'
}

class DocumentManager {
  async uploadDocument(file: File, metadata: DocumentMetadata): Promise<Document> {
    // 1. 文件类型检测和验证
    const fileType = await this.detectFileType(file);
    
    // 2. 内容提取
    const extractedContent = await this.extractContent(file, fileType);
    
    // 3. 结构化处理
    const structuredData = await this.structureDocument(extractedContent);
    
    // 4. 自动标签生成
    const autoTags = await this.generateAutoTags(extractedContent);
    
    // 5. 向量化处理
    const embeddings = await this.generateEmbeddings(extractedContent);
    
    // 6. 保存到数据库
    const document: Document = {
      ...this.createDocumentFromContent(extractedContent, metadata),
      processing: {
        embeddings,
        extractedEntities: await this.extractEntities(extractedContent),
        relationships: await this.findRelationships(extractedContent),
        summary: await this.generateSummary(extractedContent)
      }
    };
    
    return await this.saveDocument(document);
  }
  
  private async extractContent(file: File, fileType: string): Promise<string> {
    switch (fileType) {
      case 'pdf':
        return await this.extractPDFContent(file);
      case 'docx':
        return await this.extractWordContent(file);
      case 'html':
        return await this.extractHTMLContent(file);
      default:
        throw new Error(`不支持的文件类型: ${fileType}`);
    }
  }
  
  private async extractPDFContent(file: File): Promise<string> {
    // 使用pdf-parse或其他PDF解析库
    const pdfParse = require('pdf-parse');
    const buffer = await file.arrayBuffer();
    const data = await pdfParse(Buffer.from(buffer));
    return data.text;
  }
}
```

#### 2. 知识检索引擎 (KnowledgeSearchEngine)
```typescript
interface SearchQuery {
  query: string;
  filters: {
    category?: DocumentCategory[];
    dateRange?: { from: Date; to: Date };
    tags?: string[];
    language?: string;
  };
  options: {
    limit: number;
    offset: number;
    includeContent: boolean;
    highlightMatches: boolean;
  };
}

interface SearchResult {
  document: Document;
  score: number;
  matches: SearchMatch[];
  explanation: string;
}

class KnowledgeSearchEngine {
  private elasticsearch: Client;
  private vectorStore: VectorStore;
  
  async hybridSearch(query: SearchQuery): Promise<SearchResult[]> {
    // 1. 关键词搜索 (Elasticsearch)
    const keywordResults = await this.keywordSearch(query);
    
    // 2. 语义搜索 (Vector similarity)
    const semanticResults = await this.semanticSearch(query);
    
    // 3. 结果融合和重排序
    const fusedResults = await this.fuseResults(keywordResults, semanticResults);
    
    // 4. 应用过滤器
    const filteredResults = await this.applyFilters(fusedResults, query.filters);
    
    return filteredResults.slice(query.options.offset, query.options.offset + query.options.limit);
  }
  
  private async keywordSearch(query: SearchQuery): Promise<SearchResult[]> {
    const searchBody = {
      query: {
        multi_match: {
          query: query.query,
          fields: ['title^3', 'content^2', 'metadata.keywords^2', 'metadata.tags'],
          type: 'best_fields',
          fuzziness: 'AUTO'
        }
      },
      highlight: {
        fields: {
          title: {},
          content: { fragment_size: 150, number_of_fragments: 3 }
        }
      },
      size: 50
    };
    
    const response = await this.elasticsearch.search({
      index: 'documents',
      body: searchBody
    });
    
    return this.parseElasticsearchResults(response);
  }
  
  private async semanticSearch(query: SearchQuery): Promise<SearchResult[]> {
    // 1. 查询向量化
    const queryEmbedding = await this.generateQueryEmbedding(query.query);
    
    // 2. 向量相似度搜索
    const vectorResults = await this.vectorStore.similaritySearch(
      queryEmbedding,
      { k: 50, threshold: 0.6 }
    );
    
    return vectorResults.map(result => ({
      document: result.document,
      score: result.similarity,
      matches: [],
      explanation: `向量相似度: ${result.similarity.toFixed(3)}`
    }));
  }
}
```

#### 3. 专业知识提取器 (KnowledgeExtractor)
```typescript
class KnowledgeExtractor {
  private nlpProcessor: NLPProcessor;
  
  async extractTechnicalKnowledge(document: Document): Promise<TechnicalKnowledge> {
    const content = document.content;
    
    return {
      protocols: await this.extractProtocols(content),
      products: await this.extractProductMentions(content),
      parameters: await this.extractTechnicalParameters(content),
      procedures: await this.extractProcedures(content),
      troubleshooting: await this.extractTroubleshooting(content),
      bestPractices: await this.extractBestPractices(content)
    };
  }
  
  private async extractProtocols(content: string): Promise<ProtocolInfo[]> {
    // 使用NLP识别实验方案
    const protocolPattern = /(?:protocol|方案|流程)[\s\S]*?(?=\n\n|\n[A-Z]|\n\d+\.)/gi;
    const matches = content.match(protocolPattern) || [];
    
    const protocols: ProtocolInfo[] = [];
    
    for (const match of matches) {
      const steps = await this.extractSteps(match);
      const materials = await this.extractMaterials(match);
      const equipment = await this.extractEquipment(match);
      
      protocols.push({
        title: await this.extractProtocolTitle(match),
        description: await this.extractProtocolDescription(match),
        steps,
        materials,
        equipment,
        duration: await this.extractDuration(match),
        difficulty: await this.assessDifficulty(match)
      });
    }
    
    return protocols;
  }
  
  private async extractProductMentions(content: string): Promise<ProductMention[]> {
    // 识别产品和试剂盒提及
    const productPatterns = [
      /(?:kit|试剂盒|reagent)[\w\s-]+/gi,
      /10[xX]\s*Genomics[\w\s-]*/gi,
      /illumina[\w\s-]*/gi,
      // 更多产品识别模式...
    ];
    
    const mentions: ProductMention[] = [];
    
    for (const pattern of productPatterns) {
      const matches = content.match(pattern) || [];
      for (const match of matches) {
        mentions.push({
          productName: match.trim(),
          context: this.getContext(content, match),
          confidence: await this.calculateConfidence(match, content),
          category: await this.categorizeProduct(match)
        });
      }
    }
    
    return mentions;
  }
  
  private async extractTechnicalParameters(content: string): Promise<TechnicalParameter[]> {
    // 提取技术参数：温度、时间、浓度等
    const parameterPatterns = [
      /(\d+(?:\.\d+)?)\s*°?C/g,        // 温度
      /(\d+(?:\.\d+)?)\s*(?:min|hour|h)/g, // 时间
      /(\d+(?:\.\d+)?)\s*(?:μM|mM|M)/g,    // 浓度
      /(\d+(?:\.\d+)?)\s*(?:rpm|g)/g,      // 转速/离心力
      // 更多参数模式...
    ];
    
    const parameters: TechnicalParameter[] = [];
    