"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Download, Share2, BookmarkPlus, Calculator } from "lucide-react"

export function SolutionPresentation() {
  const [selectedSolution, setSelectedSolution] = useState("standard")

  const solutions = {
    standard: {
      name: "标准单细胞RNA测序方案",
      cost: 85000,
      timeline: "4-6周",
      quality: 90,
    },
    premium: {
      name: "高通量单细胞多组学方案",
      cost: 150000,
      timeline: "6-8周",
      quality: 95,
    },
  }

  const products = [
    {
      name: "10x Genomics Chromium X",
      spec: "单细胞分离系统",
      quantity: 1,
      price: 25000,
    },
    {
      name: "Chromium Single Cell 3' v3.1",
      spec: "试剂盒",
      quantity: 4,
      price: 15000,
    },
  ]

  const workflow = [
    { step: 1, title: "样本准备", duration: "1-2天", description: "细胞分离和质量检测" },
    { step: 2, title: "单细胞分离", duration: "1天", description: "使用10x Genomics平台" },
    { step: 3, title: "文库构建", duration: "2-3天", description: "cDNA合成和扩增" },
    { step: 4, title: "测序分析", duration: "3-5天", description: "NovaSeq 6000测序" },
    { step: 5, title: "数据分析", duration: "1-2周", description: "生物信息学分析" },
  ]

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">技术方案展示</h2>
          <p className="text-slate-600 mt-1">为北京大学医学院定制的单细胞测序方案</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <BookmarkPlus className="h-4 w-4 mr-2" />
            收藏
          </Button>
          <Button variant="outline" size="sm">
            <Share2 className="h-4 w-4 mr-2" />
            分享
          </Button>
          <Button size="sm">
            <Download className="h-4 w-4 mr-2" />
            导出PDF
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-3">
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">方案概览</TabsTrigger>
              <TabsTrigger value="products">产品配置</TabsTrigger>
              <TabsTrigger value="workflow">实验流程</TabsTrigger>
              <TabsTrigger value="cost">成本分析</TabsTrigger>
              <TabsTrigger value="risk">风险评估</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-blue-600">¥85,000</div>
                    <div className="text-sm text-slate-600 mt-1">预估总成本</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-green-600">4-6周</div>
                    <div className="text-sm text-slate-600 mt-1">完成时间</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-purple-600">90%</div>
                    <div className="text-sm text-slate-600 mt-1">质量评分</div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>技术路线图</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                      <div>
                        <div className="font-medium">10x Genomics单细胞平台</div>
                        <div className="text-sm text-slate-600">高通量、高质量的单细胞分离技术</div>
                      </div>
                      <Badge>推荐</Badge>
                    </div>
                    <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                      <div>
                        <div className="font-medium">NovaSeq 6000测序</div>
                        <div className="text-sm text-slate-600">高深度测序，确保数据质量</div>
                      </div>
                      <Badge variant="outline">标准</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="products" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>产品清单</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2">产品名称</th>
                          <th className="text-left py-2">规格</th>
                          <th className="text-center py-2">数量</th>
                          <th className="text-right py-2">单价</th>
                          <th className="text-right py-2">小计</th>
                        </tr>
                      </thead>
                      <tbody>
                        {products.map((product, index) => (
                          <tr key={index} className="border-b">
                            <td className="py-3 font-medium">{product.name}</td>
                            <td className="py-3 text-slate-600">{product.spec}</td>
                            <td className="py-3 text-center">{product.quantity}</td>
                            <td className="py-3 text-right">¥{product.price.toLocaleString()}</td>
                            <td className="py-3 text-right font-medium">
                              ¥{(product.price * product.quantity).toLocaleString()}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="workflow" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>实验流程</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {workflow.map((step, index) => (
                      <div key={step.step} className="flex items-start space-x-4">
                        <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                          {step.step}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{step.title}</h4>
                            <Badge variant="outline">{step.duration}</Badge>
                          </div>
                          <p className="text-sm text-slate-600 mt-1">{step.description}</p>
                          {index < workflow.length - 1 && <div className="w-px h-6 bg-slate-200 ml-4 mt-2"></div>}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="cost" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>成本构成</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-slate-600">设备使用费</span>
                        <span className="font-medium">¥35,000</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-slate-600">试剂耗材</span>
                        <span className="font-medium">¥30,000</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-slate-600">测序服务</span>
                        <span className="font-medium">¥15,000</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-slate-600">数据分析</span>
                        <span className="font-medium">¥5,000</span>
                      </div>
                      <div className="border-t pt-2 flex items-center justify-between font-semibold">
                        <span>总计</span>
                        <span className="text-lg">¥85,000</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>ROI计算器</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm text-slate-600">预期发表论文数</label>
                        <div className="text-2xl font-bold">2-3篇</div>
                      </div>
                      <div>
                        <label className="text-sm text-slate-600">预计影响因子</label>
                        <div className="text-2xl font-bold">8-12</div>
                      </div>
                      <div>
                        <label className="text-sm text-slate-600">投资回报率</label>
                        <div className="text-2xl font-bold text-green-600">350%</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="risk" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>风险评估</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-green-800">技术风险</div>
                          <div className="text-sm text-green-600">成熟技术平台，风险较低</div>
                        </div>
                        <Badge className="bg-green-100 text-green-800">低风险</Badge>
                      </div>
                    </div>
                    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-yellow-800">时间风险</div>
                          <div className="text-sm text-yellow-600">样本质量可能影响进度</div>
                        </div>
                        <Badge className="bg-yellow-100 text-yellow-800">中风险</Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full">
                <Calculator className="h-4 w-4 mr-2" />
                生成报价单
              </Button>
              <Button variant="outline" className="w-full">
                方案对比
              </Button>
              <Button variant="outline" className="w-full">
                联系专家
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>相关推荐</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="text-sm">
                <div className="font-medium">空间转录组方案</div>
                <div className="text-slate-600">适合组织样本分析</div>
              </div>
              <div className="text-sm">
                <div className="font-medium">多组学联合分析</div>
                <div className="text-slate-600">更全面的细胞表征</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
