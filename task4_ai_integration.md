# 任务4: AI对话引擎集成

## 给Cursor/Augment的提示词

你是一个AI集成专家，需要开发一个专业的单细胞测序领域的AI对话引擎。

### 核心需求
- 集成OpenAI GPT-4 API
- 构建专业的单细胞测序知识体系
- 实现智能需求收集和方案推荐
- 支持多轮对话和上下文记忆
- 提供结构化的输出格式

### 技术架构
- OpenAI API集成
- 向量数据库（Pinecone/Chroma）
- 知识图谱构建
- 提示词工程（Prompt Engineering）
- 输出解析和验证

### AI服务核心组件

#### 1. AI对话管理器 (AIConversationManager)
```typescript
class AIConversationManager {
  private openai: OpenAI;
  private vectorStore: VectorStore;
  private knowledgeBase: KnowledgeBase;
  
  async processUserMessage(
    message: string, 
    context: ConversationContext
  ): Promise<AIResponse> {
    // 1. 消息预处理
    // 2. 意图识别
    // 3. 知识检索
    // 4. 响应生成
    // 5. 结构化输出解析
  }
  
  async generateExperimentalProtocol(
    requirements: UserRequirement
  ): Promise<ExperimentalProtocol> {
    // 基于需求生成详细实验方案
  }
}
```

#### 2. 提示词模板系统 (PromptTemplateSystem)
```typescript
interface PromptTemplate {
  id: string;
  name: string;
  category: 'requirement_collection' | 'protocol_generation' | 'product_recommendation';
  template: string;
  variables: string[];
  outputSchema: JSONSchema;
}

class PromptTemplateManager {
  // 需求收集提示词
  private requirementCollectionPrompt = `
你是一位专业的单细胞测序技术专家。你需要通过对话收集客户的实验需求。

当前对话上下文：
{conversationHistory}

已收集的需求信息：
{currentRequirements}

客户最新消息：
{userMessage}

请根据以下原则回复：
1. 专业但友好的语调
2. 针对性提问，避免重复已知信息
3. 提供专业建议和选项
4. 识别并提取结构化需求信息

如果信息不足，请继续提问。如果信息充足，请确认需求并提议生成实验方案。

请以JSON格式输出响应：
{
  "response": "回复文本",
  "extractedRequirements": {
    "tissueType": "string",
    "sampleCount": "number",
    "sequencingDepth": "string",
    "researchGoal": "string[]",
    "budget": "number",
    "timeline": "string",
    "specialRequirements": "string"
  },
  "nextQuestions": "string[]",
  "readyForProtocol": "boolean"
}
`;

  // 方案生成提示词
  private protocolGenerationPrompt = `
你是单细胞测序领域的顶级专家。基于以下用户需求，生成详细的实验方案。

用户需求：
{userRequirements}

可用产品信息：
{availableProducts}

行业最佳实践：
{bestPractices}

请生成一个全面的单细胞测序实验方案，包括：

1. 实验设计概述
2. 详细的实验步骤
3. 推荐的产品和试剂
4. 质量控制要点
5. 预期结果和数据分析策略
6. 成本估算
7. 时间安排
8. 潜在风险和解决方案

输出格式要求：
{
  "protocolOverview": {
    "title": "方案标题",
    "description": "方案描述",
    "applicableScenarios": ["适用场景"],
    "estimatedDuration": "预计时间",
    "difficultyLevel": "难度等级"
  },
  "experimentalSteps": [
    {
      "stepNumber": 1,
      "title": "步骤标题",
      "description": "详细描述",
      "duration": "所需时间",
      "materials": ["所需材料"],
      "equipment": ["所需设备"],
      "criticalPoints": ["关键注意事项"],
      "qualityControl": ["质控要点"]
    }
  ],
  "recommendedProducts": [
    {
      "productId": "产品ID",
      "productName": "产品名称",
      "quantity": "所需数量",
      "purpose": "用途说明",
      "alternatives": ["替代产品"],
      "criticalityLevel": "重要程度"
    }
  ],
  "costAnalysis": {
    "totalEstimatedCost": 0,
    "costBreakdown": [
      {
        "category": "成本类别",
        "amount": 0,
        "percentage": 0,
        "items": ["具体项目"]
      }
    ],
    "costOptimizationSuggestions": ["成本优化建议"]
  },
  "timeline": {
    "totalDuration": "总时长",
    "phases": [
      {
        "name": "阶段名称",
        "duration": "持续时间",
        "description": "阶段描述",
        "deliverables": ["交付物"]
      }
    ]
  },
  "qualityAssurance": {
    "checkpoints": ["质控检查点"],
    "expectedResults": ["预期结果"],
    "troubleshooting": [
      {
        "issue": "可能问题",
        "solution": "解决方案"
      }
    ]
  },
  "dataAnalysis": {
    "analysisWorkflow": ["分析流程"],
    "requiredSoftware": ["所需软件"],
    "expectedOutputs": ["预期输出"],
    "interpretationGuidelines": ["结果解读指南"]
  }
}
`;

  // 产品推荐提示词
  private productRecommendationPrompt = `
作为单细胞测序产品专家，基于用户需求推荐最适合的产品组合。

用户需求：
{userRequirements}

产品数据库：
{productDatabase}

用户历史偏好：
{userPreferences}

请分析并推荐产品，考虑以下因素：
1. 技术适配性
2. 成本效益
3. 质量可靠性
4. 供货稳定性
5. 技术支持
6. 用户反馈

输出格式：
{
  "primaryRecommendations": [
    {
      "productId": "产品ID",
      "recommendationScore": 0.95,
      "reasonsForRecommendation": ["推荐理由"],
      "suitabilityAnalysis": "适用性分析",
      "costBenefit": "成本效益分析"
    }
  ],
  "alternativeOptions": [
    {
      "productId": "产品ID",
      "comparisonWithPrimary": "与主推产品对比",
      "scenarios": ["适用场景"]
    }
  ],
  "costComparison": {
    "budgetFit": "预算匹配度",
    "valueProposition": "价值主张",
    "savingOpportunities": ["节省机会"]
  }
}
`;
}
```

#### 3. 知识检索系统 (KnowledgeRetrievalSystem)
```typescript
class KnowledgeRetrievalSystem {
  private vectorStore: VectorStore;
  private documentIndex: Map<string, Document>;
  
  async initializeKnowledgeBase(): Promise<void> {
    // 初始化向量数据库
    await this.loadScientificLiterature();
    await this.loadProductDocuments();
    await this.loadProtocolDatabase();
    await this.loadTroubleshootingGuides();
  }
  
  async retrieveRelevantKnowledge(
    query: string, 
    context: string
  ): Promise<RelevantKnowledge> {
    // 1. 查询向量化
    const queryEmbedding = await this.generateEmbedding(query);
    
    // 2. 相似度搜索
    const similarDocuments = await this.vectorStore.similaritySearch(
      queryEmbedding, 
      { k: 10, threshold: 0.7 }
    );
    
    // 3. 结果重排序
    const rankedResults = await this.rerankResults(similarDocuments, context);
    
    // 4. 知识整合
    return this.synthesizeKnowledge(rankedResults);
  }
  
  private async loadScientificLiterature(): Promise<void> {
    const literatureData = [
      {
        title: "Single-cell RNA sequencing technologies and applications",
        content: "详细的单细胞测序技术综述...",
        category: "technology_overview",
        tags: ["scRNA-seq", "technology", "applications"]
      },
      // 更多文献数据...
    ];
    
    for (const doc of literatureData) {
      const embedding = await this.generateEmbedding(doc.content);
      await this.vectorStore.addDocument(doc, embedding);
    }
  }
}
```

#### 4. 对话上下文管理 (ConversationContextManager)
```typescript
interface ConversationContext {
  sessionId: string;
  userId: string;
  currentStage: 'requirement_collection' | 'protocol_generation' | 'optimization';
  collectedRequirements: Partial<UserRequirement>;
  conversationHistory: Message[];
  lastIntent: string;
  extractedEntities: Record<string, any>;
  preferredProducts: string[];
  constraints: {
    budget?: number;
    timeline?: string;
    restrictions?: string[];
  };
}

class ConversationContextManager {
  private contexts: Map<string, ConversationContext> = new Map();
  
  async updateContext(
    sessionId: string, 
    updates: Partial<ConversationContext>
  ): Promise<ConversationContext> {
    const currentContext = this.contexts.get(sessionId) || this.createNewContext(sessionId);
    const updatedContext = { ...currentContext, ...updates };
    this.contexts.set(sessionId, updatedContext);
    return updatedContext;
  }
  
  async analyzeIntent(message: string): Promise<string> {
    // 使用机器学习模型或规则引擎分析用户意图
    const intentClassificationPrompt = `
    分析以下用户消息的意图，从以下类别中选择：
    - requirement_specification: 用户在说明实验需求
    - question_asking: 用户在询问问题
    - protocol_review: 用户在评价或修改方案
    - product_inquiry: 用户在询问产品信息
    - cost_concern: 用户关心成本问题
    - timeline_concern: 用户关心时间安排
    
    用户消息: "${message}"
    
    只回复意图类别，不要其他内容。
    `;
    
    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [{ role: "user", content: intentClassificationPrompt }],
      temperature: 0.1
    });
    
    return response.choices[0].message.content?.trim() || 'unknown';
  }
}
```

#### 5. 输出解析和验证 (OutputParser)
```typescript
class OutputParser {
  async parseStructuredResponse(
    response: string, 
    expectedSchema: JSONSchema
  ): Promise<any> {
    try {
      // 1. 提取JSON内容
      const jsonMatch = response.match(/```json\n([\s\S]*?)\n```/) || 
                       response.match(/\{[\s\S]*\}/);
      
      if (!jsonMatch) {
        throw new Error("未找到有效的JSON响应");
      }
      
      // 2. 解析JSON
      const parsedData = JSON.parse(jsonMatch[1] || jsonMatch[0]);
      
      // 3. 数据验证
      const validationResult = await this.validateAgainstSchema(parsedData, expectedSchema);
      
      if (!validationResult.valid) {
        throw new Error(`数据验证失败: ${validationResult.errors.join(', ')}`);
      }
      
      return parsedData;
    } catch (error) {
      console.error('输出解析错误:', error);
      throw new Error(`无法解析AI响应: ${error.message}`);
    }
  }
  
  private async validateAgainstSchema(data: any, schema: JSONSchema): Promise<ValidationResult> {
    // 使用Ajv或其他JSON Schema验证库
    const ajv = new Ajv();
    const validate = ajv.compile(schema);
    const valid = validate(data);
    
    return {
      valid,
      errors: valid ? [] : validate.errors?.map(err => err.message) || []
    };
  }
}
```

#### 6. AI服务主控制器 (AIServiceController)
```typescript
class AIServiceController {
  private conversationManager: AIConversationManager;
  private contextManager: ConversationContextManager;
  private knowledgeRetrieval: KnowledgeRetrievalSystem;
  private outputParser: OutputParser;
  
  async handleChatMessage(request: ChatMessageRequest): Promise<ChatMessageResponse> {
    try {
      // 1. 获取对话上下文
      const context = await this.contextManager.getContext(request.sessionId);
      
      // 2. 分析用户意图
      const intent = await this.contextManager.analyzeIntent(request.message);
      
      // 3. 检索相关知识
      const relevantKnowledge = await this.knowledgeRetrieval.retrieveRelevantKnowledge(
        request.message, 
        JSON.stringify(context)
      );
      
      // 4. 生成AI响应
      const aiResponse = await this.conversationManager.processUserMessage(
        request.message,
        { ...context, intent, relevantKnowledge }
      );
      
      // 5. 解析结构化输出
      const parsedResponse = await this.outputParser.parseStructuredResponse(
        aiResponse.content,
        this.getResponseSchema(intent)
      );
      
      // 6. 更新对话上下文
      await this.contextManager.updateContext(request.sessionId, {
        lastIntent: intent,
        collectedRequirements: {
          ...context.collectedRequirements,
          ...parsedResponse.extractedRequirements
        },
        conversationHistory: [
          ...context.conversationHistory,
          { role: 'user', content: request.message, timestamp: new Date() },
          { role: 'assistant', content: parsedResponse.response, timestamp: new Date() }
        ]
      });
      
      return {
        success: true,
        response: parsedResponse.response,
        extractedRequirements: parsedResponse.extractedRequirements,
        nextQuestions: parsedResponse.nextQuestions,
        readyForProtocol: parsedResponse.readyForProtocol,
        suggestedActions: this.generateSuggestedActions(parsedResponse, context)
      };
      
    } catch (error) {
      console.error('AI服务处理错误:', error);
      return {
        success: false,
        error: '抱歉，处理您的请求时遇到了问题，请稍后再试。',
        fallbackResponse: this.generateFallbackResponse(request.message)
      };
    }
  }
  
  async generateProtocol(requirements: UserRequirement): Promise<ExperimentalProtocol> {
    // 生成完整的实验方案
    const relevantProducts = await this.knowledgeRetrieval.retrieveRelevantProducts(requirements);
    const bestPractices = await this.knowledgeRetrieval.retrieveBestPractices(requirements);
    
    const protocolResponse = await this.conversationManager.generateExperimentalProtocol({
      requirements,
      availableProducts: relevantProducts,
      bestPractices
    });
    
    return await this.outputParser.parseStructuredResponse(
      protocolResponse.content,
      this.getProtocolSchema()
    );
  }
}
```

### 错误处理和容错机制

#### 1. API调用重试机制
```typescript
class APIRetryHandler {
  async callWithRetry<T>(
    apiCall: () => Promise<T>, 
    maxRetries: number = 3
  ): Promise<T> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        if (attempt === maxRetries) throw error;
        
        const delay = Math.pow(2, attempt) * 1000; // 指数退避
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    throw new Error('API调用失败');
  }
}
```

#### 2. 回退响应机制
```typescript
class FallbackResponseGenerator {
  generateFallbackResponse(userMessage: string, context?: ConversationContext): string {
    const fallbackTemplates = [
      "我理解您的需求，让我为您查找相关信息...",
      "这是一个很好的问题，让我基于我的专业知识为您解答...",
      "基于您提到的情况，我建议我们先确认一些关键信息..."
    ];
    
    return fallbackTemplates[Math.floor(Math.random() * fallbackTemplates.length)];
  }
}
```

### 性能优化

#### 1. 响应缓存
```typescript
class ResponseCache {
  private cache: Map<string, CachedResponse> = new Map();
  
  async getCachedResponse(key: string): Promise<CachedResponse | null> {
    const cached = this.cache.get(key);
    if (cached && cached.expiry > Date.now()) {
      return cached;
    }
    return null;
  }
  
  async setCachedResponse(key: string, response: any, ttl: number = 3600000): Promise<void> {
    this.cache.set(key, {
      data: response,
      expiry: Date.now() + ttl
    });
  }
}
```

#### 2. 批量处理
```typescript
class BatchProcessor {
  private queue: ProcessingTask[] = [];
  private processing = false;
  
  async addTask(task: ProcessingTask): Promise<void> {
    this.queue.push(task);
    if (!this.processing) {
      this.processBatch();
    }
  }
  
  private async processBatch(): Promise<void> {
    this.processing = true;
    while (this.queue.length > 0) {
      const batch = this.queue.splice(0, 5); // 批量处理5个任务
      await Promise.all(batch.map(task => this.processTask(task)));
    }
    this.processing = false;
  }
}
```

### 监控和日志

#### 1. AI服务监控
```typescript
class AIServiceMonitor {
  async logAPIUsage(request: any, response: any, duration: number): Promise<void> {
    const logEntry = {
      timestamp: new Date(),
      requestType: request.type,
      tokens: response.usage?.total_tokens || 0,
      duration,
      success: response.success,
      error: response.error
    };
    
    await this.saveToDatabase(logEntry);
  }
  
  async generateUsageReport(): Promise<UsageReport> {
    // 生成API使用情况报告
    return {
      totalRequests: 0,
      averageResponseTime: 0,
      tokenUsage: 0,
      errorRate: 0,
      costEstimate: 0
    };
  }
}
```

### 测试策略

#### 1. AI响应测试
```typescript
describe('AI Service Tests', () => {
  test('should extract requirements correctly', async () => {
    const mockMessage = "我需要做心脏组织的单细胞转录组测序，样本数量是10个";
    const response = await aiService.processMessage(mockMessage, mockContext);
    
    expect(response.extractedRequirements.tissueType).toBe('heart');
    expect(response.extractedRequirements.sampleCount).toBe(10);
  });
  
  test('should generate valid protocol', async () => {
    const requirements = {
      tissueType: 'heart',
      sampleCount: 10,
      sequencingDepth: 'standard',
      researchGoal: ['cell_type_identification']
    };
    
    const protocol = await aiService.generateProtocol(requirements);
    expect(protocol.experimentalSteps).toBeDefined();
    expect(protocol.recommendedProducts).toHaveLength(expect.any(Number));
  });
});
```

### 部署配置

#### 1. 环境变量
```typescript
interface AIServiceConfig {
  OPENAI_API_KEY: string;
  OPENAI_MODEL: string;
  VECTOR_DB_URL: string;
  MAX_TOKENS: number;
  TEMPERATURE: number;
  CACHE_TTL: number;
}
```

#### 2. Docker配置
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

### 交付物
- 完整的AI对话引擎
- 知识检索和向量化系统
- 结构化输出解析器
- 对话上下文管理
- 错误处理和容错机制
- 性能监控和日志系统
- 完整的测试套件
- 部署配置和文档

请按照以上要求开发一个智能、可靠、专业的AI对话引擎，确保能够准确理解用户需求并生成高质量的单细胞测序实验方案。