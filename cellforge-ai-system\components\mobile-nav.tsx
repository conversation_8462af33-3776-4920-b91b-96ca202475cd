"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { useAuth } from "@/contexts/auth-context"
import { cn } from "@/lib/utils"
import { Menu, MessageSquare, Users, FileText, Database, BarChart2, Settings, LogOut } from "lucide-react"

export function MobileNav() {
  const [open, setOpen] = useState(false)
  const pathname = usePathname()
  const { user, hasPermission, logout } = useAuth()

  const navItems = [
    {
      title: "对话",
      href: "/",
      icon: MessageSquare,
    },
    {
      title: "客户",
      href: "/customers",
      icon: Users,
      permission: "view_customers",
    },
    {
      title: "方案",
      href: "/solutions",
      icon: FileText,
      permission: "view_solutions",
    },
    {
      title: "知识库",
      href: "/knowledge",
      icon: Database,
      permission: "view_knowledge",
    },
    {
      title: "分析",
      href: "/analytics",
      icon: BarChart2,
      permission: "view_dashboard",
    },
    {
      title: "设置",
      href: "/settings",
      icon: Settings,
    },
  ]

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">打开菜单</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-64 p-0">
        <div className="p-6 border-b">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">CF</span>
            </div>
            <h1 className="text-xl font-semibold text-slate-900">CellForge AI</h1>
          </div>
          {user && (
            <div className="mt-4 pt-4 border-t border-slate-200">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-slate-200 rounded-full flex items-center justify-center">
                  <span className="text-slate-700 font-medium text-sm">{user.name.charAt(0)}</span>
                </div>
                <div>
                  <div className="text-sm font-medium">{user.name}</div>
                  <div className="text-xs text-slate-500">{user.email}</div>
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="py-4">
          <nav className="flex flex-col space-y-1">
            {navItems.map((item) => {
              // Skip rendering if user doesn't have permission
              if (item.permission && !hasPermission(item.permission)) {
                return null
              }

              const isActive = pathname === item.href
              const Icon = item.icon

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={() => setOpen(false)}
                  className={cn(
                    "flex items-center py-3 px-6 text-sm font-medium transition-colors",
                    isActive
                      ? "bg-blue-50 text-blue-600 border-r-2 border-blue-600"
                      : "text-slate-600 hover:bg-slate-50",
                  )}
                >
                  <Icon className={cn("h-4 w-4 mr-3", isActive ? "text-blue-600" : "text-slate-500")} />
                  {item.title}
                </Link>
              )
            })}
            <button
              onClick={() => {
                logout()
                setOpen(false)
              }}
              className="flex items-center py-3 px-6 text-sm font-medium text-slate-600 hover:bg-slate-50 transition-colors"
            >
              <LogOut className="h-4 w-4 mr-3 text-slate-500" />
              退出登录
            </button>
          </nav>
        </div>
      </SheetContent>
    </Sheet>
  )
}
