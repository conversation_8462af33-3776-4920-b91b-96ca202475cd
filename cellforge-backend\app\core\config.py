from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "CellForge AI Backend"
    DEBUG: bool = False
    
    # 数据库配置
    DATABASE_URL: str = "postgresql+asyncpg://user:password@localhost/cellforge_ai"
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # 认证配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # AI 服务配置
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-4"
    
    # 向量数据库配置
    CHROMA_PERSIST_DIRECTORY: str = "./chroma_db"
    
    # 文件存储配置
    UPLOAD_DIRECTORY: str = "./uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    
    # 单细胞分析配置
    SCANPY_SETTINGS_VERBOSITY: int = 1
    SCANPY_SETTINGS_N_JOBS: int = -1
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 全局设置实例
settings = Settings() 