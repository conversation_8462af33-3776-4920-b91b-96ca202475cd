"use client"

import type React from "react"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { useAuth } from "@/contexts/auth-context"
import { MessageSquare, Users, FileText, Database, BarChart2, Settings } from "lucide-react"

interface NavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  permission?: string
}

export function MainNav({ className }: { className?: string }) {
  const pathname = usePathname()
  const { hasPermission } = useAuth()

  const navItems: NavItem[] = [
    {
      title: "对话",
      href: "/",
      icon: MessageSquare,
    },
    {
      title: "客户",
      href: "/customers",
      icon: Users,
      permission: "view_customers",
    },
    {
      title: "方案",
      href: "/solutions",
      icon: FileText,
      permission: "view_solutions",
    },
    {
      title: "知识库",
      href: "/knowledge",
      icon: Database,
      permission: "view_knowledge",
    },
    {
      title: "分析",
      href: "/analytics",
      icon: BarChart2,
      permission: "view_dashboard",
    },
    {
      title: "设置",
      href: "/settings",
      icon: Settings,
    },
  ]

  return (
    <nav className={cn("flex items-center space-x-4 lg:space-x-6", className)}>
      {navItems.map((item) => {
        // Skip rendering if user doesn't have permission
        if (item.permission && !hasPermission(item.permission)) {
          return null
        }

        const isActive = pathname === item.href
        const Icon = item.icon

        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex items-center text-sm font-medium transition-colors hover:text-blue-600",
              isActive ? "text-blue-600" : "text-slate-600",
            )}
          >
            <Icon className={cn("h-4 w-4 mr-2", isActive ? "text-blue-600" : "text-slate-600")} />
            {item.title}
          </Link>
        )
      })}
    </nav>
  )
}
