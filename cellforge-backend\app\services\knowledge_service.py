"""
知识库服务
"""
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class KnowledgeService:
    """知识库服务类"""
    
    def __init__(self):
        # 模拟知识库数据
        self.knowledge_base = [
            {
                "id": 1,
                "title": "单细胞RNA测序基础",
                "content": "单细胞RNA测序(scRNA-seq)是一种在单细胞水平上分析基因表达的技术...",
                "category": "基础知识",
                "keywords": ["单细胞", "RNA测序", "基因表达"]
            },
            {
                "id": 2,
                "title": "10x Genomics平台介绍",
                "content": "10x Genomics是目前最主流的单细胞测序平台之一...",
                "category": "技术平台",
                "keywords": ["10x", "平台", "技术"]
            },
            {
                "id": 3,
                "title": "样本制备注意事项",
                "content": "单细胞测序的样本制备是实验成功的关键步骤...",
                "category": "实验流程",
                "keywords": ["样本制备", "实验", "质控"]
            }
        ]
    
    async def search_knowledge(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索知识库"""
        try:
            # 简单的关键词匹配搜索
            results = []
            query_lower = query.lower()
            
            for item in self.knowledge_base:
                score = 0
                
                # 标题匹配
                if query_lower in item["title"].lower():
                    score += 3
                
                # 内容匹配
                if query_lower in item["content"].lower():
                    score += 2
                
                # 关键词匹配
                for keyword in item["keywords"]:
                    if query_lower in keyword.lower() or keyword.lower() in query_lower:
                        score += 1
                
                if score > 0:
                    results.append({
                        **item,
                        "relevance_score": score
                    })
            
            # 按相关性排序
            results.sort(key=lambda x: x["relevance_score"], reverse=True)
            
            # 返回前top_k个结果
            return results[:top_k]
            
        except Exception as e:
            logger.error(f"知识库搜索失败: {e}")
            return []
    
    async def get_knowledge_by_id(self, knowledge_id: int) -> Dict[str, Any]:
        """根据ID获取知识条目"""
        try:
            for item in self.knowledge_base:
                if item["id"] == knowledge_id:
                    return item
            return {}
        except Exception as e:
            logger.error(f"获取知识条目失败: {e}")
            return {}
    
    async def add_knowledge(self, knowledge_data: Dict[str, Any]) -> bool:
        """添加知识条目"""
        try:
            # 生成新ID
            max_id = max([item["id"] for item in self.knowledge_base], default=0)
            knowledge_data["id"] = max_id + 1
            
            self.knowledge_base.append(knowledge_data)
            logger.info(f"添加知识条目: {knowledge_data['title']}")
            return True
            
        except Exception as e:
            logger.error(f"添加知识条目失败: {e}")
            return False
    
    async def update_knowledge(self, knowledge_id: int, knowledge_data: Dict[str, Any]) -> bool:
        """更新知识条目"""
        try:
            for i, item in enumerate(self.knowledge_base):
                if item["id"] == knowledge_id:
                    self.knowledge_base[i].update(knowledge_data)
                    logger.info(f"更新知识条目: {knowledge_id}")
                    return True
            return False
            
        except Exception as e:
            logger.error(f"更新知识条目失败: {e}")
            return False
    
    async def delete_knowledge(self, knowledge_id: int) -> bool:
        """删除知识条目"""
        try:
            for i, item in enumerate(self.knowledge_base):
                if item["id"] == knowledge_id:
                    del self.knowledge_base[i]
                    logger.info(f"删除知识条目: {knowledge_id}")
                    return True
            return False
            
        except Exception as e:
            logger.error(f"删除知识条目失败: {e}")
            return False
