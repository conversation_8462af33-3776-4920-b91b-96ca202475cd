"""
对话相关Pydantic模式
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class MessageRole(str, Enum):
    """消息角色枚举"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"

class ConversationType(str, Enum):
    """对话类型枚举"""
    GENERAL = "general"
    SINGLE_CELL_CONSULTING = "single_cell_consulting"
    TECHNICAL_SUPPORT = "technical_support"
    SALES_INQUIRY = "sales_inquiry"

class MessageBase(BaseModel):
    """消息基础模式"""
    role: MessageRole
    content: str = Field(..., min_length=1)
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class MessageCreate(MessageBase):
    """消息创建模式"""
    conversation_id: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None

class MessageResponse(MessageBase):
    """消息响应模式"""
    id: int
    conversation_id: int
    metadata: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True

class ConversationBase(BaseModel):
    """对话基础模式"""
    title: Optional[str] = Field(None, max_length=255)
    conversation_type: ConversationType = ConversationType.GENERAL

class ConversationCreate(ConversationBase):
    """对话创建模式"""
    initial_message: Optional[str] = None

class ConversationUpdate(BaseModel):
    """对话更新模式"""
    title: Optional[str] = Field(None, max_length=255)
    status: Optional[str] = None

class ConversationResponse(ConversationBase):
    """对话响应模式"""
    id: int
    user_id: int
    status: str
    created_at: datetime
    updated_at: Optional[datetime]
    message_count: int = 0
    last_message_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class ConversationWithMessages(ConversationResponse):
    """包含消息的对话响应"""
    messages: List[MessageResponse] = []

class ConversationRequest(BaseModel):
    """对话请求模式"""
    message: str = Field(..., min_length=1)
    conversation_id: Optional[int] = None
    conversation_type: ConversationType = ConversationType.SINGLE_CELL_CONSULTING
    history: Optional[List[Dict[str, str]]] = []
    context: Optional[Dict[str, Any]] = None

class AIResponse(BaseModel):
    """AI响应模式"""
    content: str
    confidence: float = Field(0.0, ge=0.0, le=1.0)
    sources: Optional[List[str]] = []
    suggestions: Optional[List[str]] = []
    metadata: Optional[Dict[str, Any]] = None

class ConversationAnalytics(BaseModel):
    """对话分析模式"""
    total_conversations: int
    active_conversations: int
    avg_messages_per_conversation: float
    avg_response_time: float
    user_satisfaction: Optional[float] = None
    common_topics: List[str] = []

class ConversationHistory(BaseModel):
    """对话历史模式"""
    conversations: List[ConversationResponse]
    total: int
    page: int
    size: int
