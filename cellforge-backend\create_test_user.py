#!/usr/bin/env python3
"""
创建测试用户脚本
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal, init_db
from app.models.user import User, UserRole, UserStatus
from app.models.conversation import Conversation, Message  # 导入对话模型
from app.core.security import get_password_hash

def create_test_users():
    """创建测试用户"""
    # 初始化数据库
    init_db()

    # 创建数据库会话
    db = SessionLocal()

    try:
        # 检查是否已存在测试用户
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print("测试用户已存在")
            return

        # 创建测试用户
        test_users = [
            {
                "email": "<EMAIL>",
                "username": "testuser",
                "password": "Test123456",
                "first_name": "Test",
                "last_name": "User",
                "organization": "CellForge AI",
                "role": UserRole.CUSTOMER,
                "status": UserStatus.ACTIVE,
                "is_verified": True
            },
            {
                "email": "<EMAIL>",
                "username": "admin",
                "password": "Admin123456",
                "first_name": "Admin",
                "last_name": "User",
                "organization": "CellForge AI",
                "role": UserRole.SUPER_ADMIN,
                "status": UserStatus.ACTIVE,
                "is_verified": True
            },
            {
                "email": "<EMAIL>",
                "username": "sales",
                "password": "Sales123456",
                "first_name": "Sales",
                "last_name": "Manager",
                "organization": "CellForge AI",
                "role": UserRole.SALES,
                "status": UserStatus.ACTIVE,
                "is_verified": True
            }
        ]

        for user_data in test_users:
            # 创建用户对象
            user = User(
                email=user_data["email"],
                username=user_data["username"],
                hashed_password=get_password_hash(user_data["password"]),
                first_name=user_data["first_name"],
                last_name=user_data["last_name"],
                organization=user_data["organization"],
                role=user_data["role"],
                status=user_data["status"],
                is_verified=user_data["is_verified"],
                preferred_language="zh",
                email_notifications=True
            )

            db.add(user)
            print(f"创建用户: {user_data['email']}")

        # 提交更改
        db.commit()
        print("测试用户创建成功！")

        print("\n可用的测试账户:")
        print("1. <EMAIL> / Test123456 (客户)")
        print("2. <EMAIL> / Admin123456 (超级管理员)")
        print("3. <EMAIL> / Sales123456 (销售)")

    except Exception as e:
        print(f"创建测试用户失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_test_users()
