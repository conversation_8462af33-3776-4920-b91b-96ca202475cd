# 任务2: 前端对话界面开发

## 给Cursor/Augment的提示词

你是一个前端UI/UX专家，需要开发一个专业的单细胞测序方案生成系统的对话界面。

### 界面需求
创建一个现代化、专业的生物技术对话界面，包含：
- 智能聊天机器人界面
- 实时的需求收集流程
- 动态表单和参数选择
- 方案展示和对比功能

### 技术要求
- React 18 + TypeScript
- Tailwind CSS（专业配色方案）
- 响应式设计
- 平滑动画效果
- 无障碍访问支持

### 具体组件开发

#### 1. 主聊天界面组件 (ChatInterface)
```typescript
// 需要实现的功能：
- 消息历史显示
- 实时打字效果
- 消息状态指示器
- 文件上传支持
- 语音输入（可选）
```

**设计要求：**
- 科技感的深蓝色主题配色
- 消息气泡区分用户和AI
- 支持Markdown渲染
- 代码块语法高亮
- 加载状态和错误处理

#### 2. 需求收集组件 (RequirementCollector)
```typescript
// 智能表单组件，包含：
interface RequirementForm {
  tissueType: string;           // 组织类型选择器
  sampleCount: number;          // 样本数量滑块
  sequencingDepth: string;      // 测序深度选择
  researchGoal: string[];       // 研究目标多选
  budget: number;               // 预算范围
  timeline: string;             // 时间要求
  specialRequirements: string;  // 特殊需求文本框
}
```

**交互设计：**
- 分步骤引导式填写
- 实时验证和提示
- 智能推荐选项
- 进度条显示
- 一键重置功能

#### 3. 方案展示组件 (ProtocolDisplay)
```typescript
// 方案卡片组件，展示：
- 实验流程图
- 成本预估表
- 时间安排甘特图
- 产品清单
- 质控要点
- 预期结果示例
```

#### 4. 产品推荐组件 (ProductRecommendation)
```typescript
// 产品展示卡片：
- 产品图片和规格
- 适用场景说明
- 价格和库存状态
- 用户评价和案例
- 一键添加到方案
- 替代产品建议
```

#### 5. 成本分析组件 (CostAnalysis)
```typescript
// 成本可视化：
- 饼图显示成本构成
- 柱状图对比不同方案
- 成本优化建议
- 批量折扣计算
- 导出报价单功能
```

### 状态管理
使用Zustand管理全局状态：

```typescript
interface AppState {
  // 对话状态
  messages: Message[];
  isTyping: boolean;
  
  // 用户需求
  requirements: UserRequirement;
  
  // 方案数据
  recommendedProtocols: Protocol[];
  selectedProtocol: Protocol | null;
  
  // UI状态
  currentStep: number;
  isLoading: boolean;
  errors: Record<string, string>;
}
```

### 样式设计要求

#### 主题配色
```css
:root {
  --primary-blue: #1e40af;
  --secondary-blue: #3b82f6;
  --accent-green: #10b981;
  --neutral-gray: #6b7280;
  --background: #f8fafc;
  --surface: #ffffff;
  --error: #ef4444;
  --warning: #f59e0b;
}
```

#### 组件样式特点
- 圆角卡片设计（rounded-lg）
- 柔和阴影效果
- 渐变背景装饰
- 微妙的hover动画
- 专业的表格和图表样式

### 交互动画
使用Framer Motion实现：
- 页面切换动画
- 消息出现动画
- 加载状态动画
- 手势操作支持

### 响应式设计
- 移动端优先设计
- 平板端适配
- 大屏幕优化
- 触摸友好的交互元素

### 无障碍功能
- 键盘导航支持
- 屏幕阅读器兼容
- 高对比度模式
- 字体大小调节

### 性能优化
- 组件懒加载
- 图片优化
- 虚拟滚动
- 缓存策略

### 开发要求
1. 组件化开发，高复用性
2. TypeScript类型安全
3. 单元测试覆盖（Jest + Testing Library）
4. Storybook组件文档
5. 代码注释清晰

### 交付物
- 完整的聊天界面组件
- 需求收集表单组件
- 方案展示和对比功能
- 响应式设计实现
- 交互动画效果
- 单元测试用例
- 组件使用文档

请创建一个专业、易用、美观的前端界面，确保用户体验流畅且符合生物技术行业的专业标准。
