import { hasPermission } from "@/lib/auth"
import KnowledgeManagement from "./KnowledgeManagement"
import { AccessDenied } from "@/components/access-denied"

export default function KnowledgePage() {
  if (!hasPermission("view_knowledge")) {
    // redirect("/auth/sign-in"); // or render an access denied page
  }

  // Import the AccessDenied component

  // Update the return statement to conditionally render
  return (
    <div className="min-h-[calc(100vh-64px)]">
      {hasPermission("view_knowledge") ? <KnowledgeManagement /> : <AccessDenied />}
    </div>
  )
}
