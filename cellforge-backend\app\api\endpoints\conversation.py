"""
对话相关API端点
"""
from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect, status
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime
import json
import logging

from app.core.database import get_db
from app.core.auth import get_current_active_user
from app.models.user import User
from app.models.conversation import Conversation, Message, ConversationType, MessageRole, ConversationStatus
from app.schemas.conversation import (
    ConversationRequest,
    AIResponse,
    ConversationResponse,
    ConversationWithMessages,
    MessageResponse,
    ConversationCreate,
    ConversationHistory
)
from app.schemas.common import BaseResponse, PaginatedResponse
from app.services.ai_service import AIService
from app.services.knowledge_service import KnowledgeService

router = APIRouter()
logger = logging.getLogger(__name__)

# AI服务实例
ai_service = AIService()
knowledge_service = KnowledgeService()


class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)


manager = ConnectionManager()


@router.post("/chat")
async def chat_with_ai(
    request: ConversationRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    与 AI 进行单细胞测序方案咨询对话
    """
    try:
        # 获取或创建对话
        conversation = None
        if request.conversation_id:
            conversation = db.query(Conversation).filter(
                Conversation.id == request.conversation_id,
                Conversation.user_id == current_user.id
            ).first()

            if not conversation:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="对话不存在"
                )
        else:
            # 创建新对话
            conversation = Conversation(
                user_id=current_user.id,
                title=request.message[:50] + "..." if len(request.message) > 50 else request.message,
                conversation_type=ConversationType(request.conversation_type),
                status=ConversationStatus.ACTIVE
            )
            db.add(conversation)
            db.flush()  # 获取ID但不提交

        # 保存用户消息
        user_message = Message(
            conversation_id=conversation.id,
            role=MessageRole.USER,
            content=request.message,
            message_metadata=request.context
        )
        db.add(user_message)

        # 获取相关知识库内容
        relevant_knowledge = await knowledge_service.search_knowledge(
            request.message,
            top_k=5
        )

        # 构建上下文
        context = {
            "user_profile": {
                "id": current_user.id,
                "organization": current_user.organization,
                "expertise": current_user.expertise_areas,
                "research_interests": current_user.research_interests
            },
            "conversation_history": request.history or [],
            "relevant_knowledge": relevant_knowledge
        }

        # 生成 AI 回复
        ai_response = await ai_service.generate_response(
            message=request.message,
            context=context,
            conversation_type=request.conversation_type
        )

        # 保存AI回复
        ai_message = Message(
            conversation_id=conversation.id,
            role=MessageRole.ASSISTANT,
            content=ai_response.content,
            message_metadata={
                "confidence": ai_response.confidence,
                "sources": ai_response.sources,
                "suggestions": ai_response.suggestions
            }
        )
        db.add(ai_message)

        # 更新对话的最后消息时间
        conversation.last_message_at = datetime.utcnow()

        # 提交所有更改
        db.commit()

        return {
            "message": ai_response.content,
            "confidence": ai_response.confidence,
            "sources": ai_response.sources or [],
            "suggestions": ai_response.suggestions or [],
            "timestamp": datetime.utcnow().isoformat(),
            "conversation_id": conversation.id
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"对话处理失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="对话处理失败"
        )


@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: int):
    """
    WebSocket 实时对话端点
    """
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)

            # 处理消息
            ai_service = AIService()
            response = await ai_service.generate_streaming_response(
                message_data["message"],
                context=message_data.get("context", {})
            )

            # 流式发送响应
            async for chunk in response:
                await manager.send_personal_message(
                    json.dumps({"type": "chunk", "content": chunk}),
                    websocket
                )

            # 发送完成信号
            await manager.send_personal_message(
                json.dumps({"type": "complete"}),
                websocket
            )

    except WebSocketDisconnect:
        manager.disconnect(websocket)


@router.get("/history")
async def get_conversation_history(
    page: int = 1,
    size: int = 20,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取用户的对话历史
    """
    try:
        # 计算偏移量
        offset = (page - 1) * size

        # 查询用户的对话
        conversations = db.query(Conversation).filter(
            Conversation.user_id == current_user.id,
            Conversation.status != ConversationStatus.DELETED
        ).order_by(
            Conversation.last_message_at.desc()
        ).offset(offset).limit(size).all()

        # 获取总数
        total = db.query(Conversation).filter(
            Conversation.user_id == current_user.id,
            Conversation.status != ConversationStatus.DELETED
        ).count()

        # 转换为响应格式
        conversation_list = []
        for conv in conversations:
            conversation_list.append({
                "id": conv.id,
                "title": conv.title,
                "conversation_type": conv.conversation_type.value,
                "status": conv.status.value,
                "created_at": conv.created_at.isoformat(),
                "updated_at": conv.updated_at.isoformat() if conv.updated_at else None,
                "last_message_at": conv.last_message_at.isoformat() if conv.last_message_at else None,
                "message_count": len(conv.messages)
            })

        return {
            "conversations": conversation_list,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }

    except Exception as e:
        logger.error(f"获取对话历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取对话历史失败"
        )


@router.delete("/history/{conversation_id}")
async def delete_conversation(
    conversation_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    删除特定对话记录
    """
    try:
        # 查找对话
        conversation = db.query(Conversation).filter(
            Conversation.id == conversation_id,
            Conversation.user_id == current_user.id
        ).first()

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )

        # 软删除
        conversation.status = ConversationStatus.DELETED
        db.commit()

        return {"message": "对话记录已删除"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除对话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除对话失败"
        )