from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime
import json

from app.core.auth import get_current_user
from app.models.user import User
from app.services.ai_service import AIService
from app.services.knowledge_service import KnowledgeService
from app.schemas.conversation import (
    ConversationRequest,
    ConversationResponse,
    ConversationHistory,
    MessageCreate
)

router = APIRouter(prefix="/conversation", tags=["智能对话"])
security = HTTPBearer()


class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)


manager = ConnectionManager()


@router.post("/chat", response_model=ConversationResponse)
async def chat_with_ai(
    request: ConversationRequest,
    current_user: User = Depends(get_current_user)
):
    """
    与 AI 进行单细胞测序方案咨询对话
    """
    try:
        ai_service = AIService()
        knowledge_service = KnowledgeService()
        
        # 获取相关知识库内容
        relevant_knowledge = await knowledge_service.search_knowledge(
            request.message, 
            top_k=5
        )
        
        # 构建上下文
        context = {
            "user_profile": {
                "id": current_user.id,
                "organization": current_user.organization,
                "expertise": current_user.expertise_areas,
                "research_interests": current_user.research_interests
            },
            "conversation_history": request.history,
            "relevant_knowledge": relevant_knowledge
        }
        
        # 生成 AI 回复
        ai_response = await ai_service.generate_response(
            message=request.message,
            context=context,
            conversation_type="single_cell_consulting"
        )
        
        # 保存对话记录
        conversation_record = {
            "user_id": current_user.id,
            "user_message": request.message,
            "ai_response": ai_response.content,
            "timestamp": datetime.utcnow(),
            "context_used": relevant_knowledge,
            "confidence_score": ai_response.confidence
        }
        
        # 这里应该保存到数据库
        # await conversation_service.save_conversation(conversation_record)
        
        return ConversationResponse(
            message=ai_response.content,
            confidence=ai_response.confidence,
            sources=ai_response.sources,
            suggestions=ai_response.suggestions,
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"对话处理失败: {str(e)}")


@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: int):
    """
    WebSocket 实时对话端点
    """
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # 处理消息
            ai_service = AIService()
            response = await ai_service.generate_streaming_response(
                message_data["message"],
                context=message_data.get("context", {})
            )
            
            # 流式发送响应
            async for chunk in response:
                await manager.send_personal_message(
                    json.dumps({"type": "chunk", "content": chunk}),
                    websocket
                )
            
            # 发送完成信号
            await manager.send_personal_message(
                json.dumps({"type": "complete"}),
                websocket
            )
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)


@router.get("/history", response_model=List[ConversationHistory])
async def get_conversation_history(
    limit: int = 50,
    offset: int = 0,
    current_user: User = Depends(get_current_user)
):
    """
    获取用户的对话历史
    """
    # 这里应该从数据库获取
    # return await conversation_service.get_user_conversations(
    #     user_id=current_user.id,
    #     limit=limit,
    #     offset=offset
    # )
    return []


@router.delete("/history/{conversation_id}")
async def delete_conversation(
    conversation_id: int,
    current_user: User = Depends(get_current_user)
):
    """
    删除特定对话记录
    """
    # 实现删除逻辑
    return {"message": "对话记录已删除"} 