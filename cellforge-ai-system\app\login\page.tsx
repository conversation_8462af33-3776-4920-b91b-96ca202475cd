"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useAuth } from "@/contexts/auth-context"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

export default function LoginPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const { login, isLoading } = useAuth()
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    try {
      await login(email, password)
      router.push("/")
    } catch (err) {
      setError("登录失败，请检查您的邮箱和密码")
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1 text-center">
          <div className="flex justify-center mb-4">
            <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-lg">CF</span>
            </div>
          </div>
          <CardTitle className="text-2xl font-bold">CellForge AI</CardTitle>
          <CardDescription>单细胞测序方案咨询系统</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">邮箱</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password">密码</Label>
                <a href="#" className="text-sm text-blue-600 hover:underline">
                  忘记密码?
                </a>
              </div>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "登录中..." : "登录"}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="text-center text-sm text-slate-600">
          <div className="w-full">
            <p>演示账户:</p>
            <p className="mt-1">
              <code className="bg-slate-100 px-1 py-0.5 rounded"><EMAIL></code> (超级管理员)
            </p>
            <p className="mt-1">
              <code className="bg-slate-100 px-1 py-0.5 rounded"><EMAIL></code> (销售人员)
            </p>
            <p className="mt-1">
              <code className="bg-slate-100 px-1 py-0.5 rounded"><EMAIL></code> (运维人员)
            </p>
            <p className="mt-1">
              <code className="bg-slate-100 px-1 py-0.5 rounded"><EMAIL></code> (客户)
            </p>
            <p className="mt-1">所有账户密码: password</p>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
