"use client"

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { TrendingUp, Users, DollarSign, Target, AlertTriangle, CheckCircle, Clock, BarChart3 } from "lucide-react"

export function AnalyticsDashboard() {
  const kpis = [
    {
      title: "月度收入",
      value: "¥2,450,000",
      change: "+12.5%",
      trend: "up",
      icon: DollarSign,
    },
    {
      title: "新增客户",
      value: "156",
      change: "+8.3%",
      trend: "up",
      icon: Users,
    },
    {
      title: "方案转化率",
      value: "68%",
      change: "+5.2%",
      trend: "up",
      icon: Target,
    },
    {
      title: "客户满意度",
      value: "4.8/5.0",
      change: "+0.2",
      trend: "up",
      icon: CheckCircle,
    },
  ]

  const salesData = [
    { name: "张经理", revenue: 850000, deals: 12, conversion: 75 },
    { name: "李经理", revenue: 720000, deals: 10, conversion: 68 },
    { name: "王经理", revenue: 680000, deals: 9, conversion: 72 },
    { name: "赵经理", revenue: 590000, deals: 8, conversion: 65 },
  ]

  const customerSegments = [
    { type: "学术研究", count: 145, percentage: 45, value: "高" },
    { type: "生物技术", count: 98, percentage: 30, value: "极高" },
    { type: "医疗机构", count: 67, percentage: 20, value: "中" },
    { type: "其他", count: 16, percentage: 5, value: "低" },
  ]

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">数据分析仪表板</h2>
          <p className="text-slate-600 mt-1">实时业务数据概览</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">导出报告</Button>
          <Button>刷新数据</Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpis.map((kpi, index) => {
          const Icon = kpi.icon
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">{kpi.title}</p>
                    <p className="text-2xl font-bold text-slate-900 mt-1">{kpi.value}</p>
                    <div className="flex items-center mt-2">
                      <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-sm text-green-600">{kpi.change}</span>
                    </div>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Icon className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sales Performance */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>销售团队业绩</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {salesData.map((person, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium">
                        {person.name[0]}
                      </div>
                      <div>
                        <div className="font-medium">{person.name}</div>
                        <div className="text-sm text-slate-600">{person.deals} 个订单</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">¥{person.revenue.toLocaleString()}</div>
                      <div className="text-sm text-slate-600">{person.conversion}% 转化率</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Revenue Trend */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>收入趋势</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-slate-500">
                <BarChart3 className="h-16 w-16 mb-4" />
                <div className="text-center">
                  <p>收入趋势图表</p>
                  <p className="text-sm">显示过去12个月的收入变化</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Customer Analysis */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>客户分布</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {customerSegments.map((segment, index) => (
                <div key={index}>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">{segment.type}</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-slate-600">{segment.count}</span>
                      <Badge variant={segment.value === "极高" ? "default" : "secondary"}>{segment.value}</Badge>
                    </div>
                  </div>
                  <Progress value={segment.percentage} className="h-2" />
                </div>
              ))}
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle>系统状态</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">AI对话系统</span>
                </div>
                <Badge className="bg-green-100 text-green-800">正常</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">方案生成</span>
                </div>
                <Badge className="bg-green-100 text-green-800">正常</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm">数据同步</span>
                </div>
                <Badge className="bg-yellow-100 text-yellow-800">延迟</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full" variant="outline">
                <Clock className="h-4 w-4 mr-2" />
                生成月度报告
              </Button>
              <Button className="w-full" variant="outline">
                <Users className="h-4 w-4 mr-2" />
                客户分析报告
              </Button>
              <Button className="w-full" variant="outline">
                <Target className="h-4 w-4 mr-2" />
                销售预测分析
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Real-time Alerts */}
      <Card>
        <CardHeader>
          <CardTitle>实时动态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm">新客户"复旦大学生命科学院"已完成方案咨询</span>
              <span className="text-xs text-slate-500">2分钟前</span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm">张经理成功签约"中科院上海生科院"项目，金额¥180,000</span>
              <span className="text-xs text-slate-500">15分钟前</span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span className="text-sm">系统检测到"华大基因"客户满意度下降，建议主动联系</span>
              <span className="text-xs text-slate-500">1小时前</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
