/**
 * API调用工具函数
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'

// API响应类型
export interface ApiResponse<T = any> {
  status: 'success' | 'error' | 'warning'
  message: string
  data?: T
  timestamp: string
}

export interface ApiError {
  status: 'error'
  message: string
  error_code?: string
  details?: Record<string, any>
  timestamp: string
}

// 用户相关类型
export interface User {
  id: number
  email: string
  username: string
  first_name?: string
  last_name?: string
  phone?: string
  organization?: string
  department?: string
  title?: string
  role: 'super_admin' | 'sales' | 'customer' | 'operations' | 'guest'
  status: 'active' | 'pending' | 'suspended' | 'deleted'
  is_verified: boolean
  created_at: string
  updated_at?: string
  last_login?: string
  preferred_language: string
  email_notifications: boolean
  research_interests?: string
  expertise_areas?: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  username: string
  password: string
  confirm_password: string
  first_name?: string
  last_name?: string
  phone?: string
  organization?: string
  department?: string
  title?: string
  role?: string
  preferred_language?: string
  email_notifications?: boolean
  research_interests?: string
  expertise_areas?: string
}

export interface TokenResponse {
  access_token: string
  token_type: string
  expires_in: number
}

// Token管理
class TokenManager {
  private static readonly TOKEN_KEY = 'cellforge_access_token'
  private static readonly REFRESH_KEY = 'cellforge_refresh_token'

  static getToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(this.TOKEN_KEY)
  }

  static setToken(token: string): void {
    if (typeof window === 'undefined') return
    localStorage.setItem(this.TOKEN_KEY, token)
  }

  static removeToken(): void {
    if (typeof window === 'undefined') return
    localStorage.removeItem(this.TOKEN_KEY)
    localStorage.removeItem(this.REFRESH_KEY)
  }

  static isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return payload.exp * 1000 < Date.now()
    } catch {
      return true
    }
  }
}

// HTTP客户端类
class ApiClient {
  private baseURL: string

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    const token = TokenManager.getToken()

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    }

    // 添加认证头
    if (token && !TokenManager.isTokenExpired(token)) {
      headers.Authorization = `Bearer ${token}`
    }

    const config: RequestInit = {
      ...options,
      headers,
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('API请求失败:', error)
      throw error
    }
  }

  // GET请求
  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' })
  }

  // POST请求
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // PUT请求
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // DELETE请求
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }
}

// 创建API客户端实例
const apiClient = new ApiClient()

// 认证API
export const authApi = {
  // 用户登录
  async login(credentials: LoginRequest): Promise<TokenResponse> {
    const response = await apiClient.post<TokenResponse>('/auth/login', credentials)
    TokenManager.setToken(response.access_token)
    return response
  },

  // 用户注册
  async register(userData: RegisterRequest): Promise<ApiResponse> {
    return apiClient.post<ApiResponse>('/auth/register', userData)
  },

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    return apiClient.get<User>('/auth/me')
  },

  // 刷新令牌
  async refreshToken(): Promise<TokenResponse> {
    const response = await apiClient.post<TokenResponse>('/auth/refresh')
    TokenManager.setToken(response.access_token)
    return response
  },

  // 修改密码
  async changePassword(data: {
    current_password: string
    new_password: string
    confirm_password: string
  }): Promise<ApiResponse> {
    return apiClient.post<ApiResponse>('/auth/change-password', data)
  },

  // 登出
  logout(): void {
    TokenManager.removeToken()
  },
}

// 对话API
export const conversationApi = {
  // 发送消息
  async sendMessage(data: {
    message: string
    conversation_id?: number
    conversation_type?: string
    history?: Array<{ role: string; content: string }>
    context?: Record<string, any>
  }): Promise<{
    message: string
    confidence: number
    sources?: string[]
    suggestions?: string[]
    timestamp: string
  }> {
    return apiClient.post('/conversation/chat', data)
  },

  // 获取对话历史
  async getHistory(page: number = 1, size: number = 20): Promise<{
    conversations: any[]
    total: number
    page: number
    size: number
  }> {
    return apiClient.get(`/conversation/history?page=${page}&size=${size}`)
  },
}

// 导出Token管理器和API客户端
export { TokenManager, apiClient }
