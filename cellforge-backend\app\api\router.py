"""
API路由汇总
"""
from fastapi import APIRouter

from app.api.endpoints import conversation, auth

# 创建主路由器
api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["authentication"]
)

api_router.include_router(
    conversation.router,
    prefix="/conversation",
    tags=["conversation"]
)

# 健康检查端点
@api_router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "cellforge-ai-backend",
        "version": "1.0.0"
    }
