"""
数据库连接和会话管理模块
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base, Session
from typing import Generator
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# SQLAlchemy 基础模型
Base = declarative_base()

# 数据库引擎 (使用SQLite以简化开发)
SQLALCHEMY_DATABASE_URL = "sqlite:///./cellforge.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},  # SQLite特定配置
    echo=settings.DEBUG,
)

# 会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

def get_db() -> Generator[Session, None, None]:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """初始化数据库"""
    try:
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        logger.info("数据库初始化成功")

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise

def close_db():
    """关闭数据库连接"""
    engine.dispose()
    logger.info("数据库连接已关闭")
