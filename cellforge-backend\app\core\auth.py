"""
认证相关逻辑
"""
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional

from app.core.database import get_db
from app.core.security import verify_token
from app.models.user import User

# HTTP Bearer 认证方案
security = HTTPBearer()

def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前认证用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )

    # 验证令牌
    user_id = verify_token(credentials.credentials)
    if user_id is None:
        raise credentials_exception

    # 查询用户
    try:
        user = db.query(User).filter(User.id == int(user_id)).first()

        if user is None:
            raise credentials_exception

        return user

    except Exception:
        raise credentials_exception

def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前活跃用户"""
    if not current_user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户未激活"
        )
    return current_user

def require_roles(allowed_roles: list[str]):
    """角色权限装饰器"""
    def role_checker(current_user: User = Depends(get_current_active_user)):
        if current_user.role.value not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )
        return current_user
    return role_checker

def require_permissions(required_permissions: list[str]):
    """权限检查装饰器"""
    def permission_checker(current_user: User = Depends(get_current_active_user)):
        # 这里可以根据用户角色检查具体权限
        # 暂时简化处理
        user_permissions = get_user_permissions(current_user.role.value)

        for permission in required_permissions:
            if permission not in user_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少权限: {permission}"
                )
        return current_user
    return permission_checker

def get_user_permissions(role: str) -> list[str]:
    """根据角色获取权限列表"""
    role_permissions = {
        "super_admin": [
            "manage_users", "manage_permissions", "manage_knowledge",
            "view_analytics", "view_customers", "view_solutions",
            "manage_system"
        ],
        "sales": [
            "view_customers", "manage_customers", "view_solutions",
            "create_solutions", "view_analytics"
        ],
        "operations": [
            "manage_knowledge", "view_analytics", "view_solutions",
            "manage_system"
        ],
        "customer": [
            "view_solutions", "create_conversations"
        ],
        "guest": [
            "view_public_content"
        ]
    }

    return role_permissions.get(role, [])
