#!/usr/bin/env python3
"""
测试配置加载
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings

def test_config():
    """测试配置加载"""
    print("=== CellForge AI 配置测试 ===")
    print(f"应用名称: {settings.APP_NAME}")
    print(f"调试模式: {settings.DEBUG}")
    print(f"数据库URL: {settings.DATABASE_URL}")
    print(f"API Base: {settings.OPENAI_API_BASE}")
    print(f"AI模型: {settings.OPENAI_MODEL}")
    
    # 检查API密钥（只显示前几位和后几位）
    if settings.OPENAI_API_KEY:
        key = settings.OPENAI_API_KEY
        if len(key) > 10:
            masked_key = key[:6] + "..." + key[-4:]
        else:
            masked_key = "***"
        print(f"API密钥: {masked_key}")
        print("✅ API密钥已配置")
    else:
        print("❌ API密钥未配置")
    
    print(f"访问令牌过期时间: {settings.ACCESS_TOKEN_EXPIRE_MINUTES}分钟")
    print(f"密钥算法: {settings.ALGORITHM}")

if __name__ == "__main__":
    test_config()
