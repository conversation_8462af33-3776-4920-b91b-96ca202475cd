"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Send, Paperclip, Mic } from "lucide-react"
// Import the DashboardLayout component
import { DashboardLayout } from "@/components/dashboard-layout"

interface Message {
  id: string
  type: "user" | "ai"
  content: string
  timestamp: Date
  status: "sending" | "sent" | "read"
}

interface RequirementData {
  organizationType: string
  sampleCount: string
  budget: string
  timeline: string
  completeness: number
}

export function ConversationInterface() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState("")
  const [isTyping, setIsTyping] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Initialize messages on client side only to avoid hydration mismatch
  useEffect(() => {
    setIsClient(true)
    setMessages([
      {
        id: "1",
        type: "ai",
        content: "您好！我是CellForge AI智能顾问。我将帮助您制定最适合的单细胞测序方案。请告诉我您的研究背景和具体需求。",
        timestamp: new Date(),
        status: "read",
      },
    ])
  }, [])

  const [requirements, setRequirements] = useState<RequirementData>({
    organizationType: "",
    sampleCount: "",
    budget: "",
    timeline: "",
    completeness: 25,
  })

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = () => {
    if (!inputValue.trim()) return

    const newMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: inputValue,
      timestamp: new Date(),
      status: "sending",
    }

    setMessages((prev) => [...prev, newMessage])
    setInputValue("")
    setIsTyping(true)

    // Simulate AI response
    setTimeout(() => {
      setIsTyping(false)
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: "ai",
        content:
          "感谢您提供的信息。基于您的需求，我建议使用10x Genomics平台进行单细胞RNA测序。这个方案在成本效益和数据质量方面都有很好的平衡。您希望了解更多技术细节吗？",
        timestamp: new Date(),
        status: "read",
      }
      setMessages((prev) => [...prev, aiResponse])

      // Update requirements
      setRequirements((prev) => ({
        ...prev,
        completeness: Math.min(prev.completeness + 15, 100),
      }))
    }, 2000)
  }

  const quickReplies = ["我需要了解成本预算", "请推荐技术方案", "查看成功案例", "联系技术专家"]

  // Wrap the component with DashboardLayout
  return (
    <DashboardLayout
      sidebar={
        <div className="p-4">
          <h3 className="font-semibold text-slate-900 mb-4">对话历史</h3>
          <div className="space-y-2">
            <Card className="p-3 cursor-pointer hover:bg-slate-50">
              <div className="text-sm font-medium">肿瘤免疫研究方案</div>
              <div className="text-xs text-slate-500">2小时前</div>
            </Card>
            <Card className="p-3 cursor-pointer hover:bg-slate-50">
              <div className="text-sm font-medium">神经发育项目咨询</div>
              <div className="text-xs text-slate-500">昨天</div>
            </Card>
          </div>
        </div>
      }
    >
      <div className="flex flex-col h-full">
        {/* Progress Header */}
        <div className="bg-white border-b border-slate-200 p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-slate-700">咨询进度</span>
            <span className="text-sm text-slate-500">{requirements.completeness}% 完成</span>
          </div>
          <Progress value={requirements.completeness} className="h-2" />
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {!isClient ? (
            // Show loading state during hydration
            <div className="flex justify-start">
              <div className="bg-white border border-slate-200 rounded-lg px-4 py-2">
                <div className="text-sm text-slate-500">加载中...</div>
              </div>
            </div>
          ) : (
            messages.map((message) => (
              <div key={message.id} className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}>
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.type === "user" ? "bg-blue-600 text-white" : "bg-white border border-slate-200 text-slate-900"
                  }`}
                >
                  <div className="text-sm">{message.content}</div>
                  <div className={`text-xs mt-1 ${message.type === "user" ? "text-blue-100" : "text-slate-500"}`}>
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))
          )}

          {isTyping && (
            <div className="flex justify-start">
              <div className="bg-white border border-slate-200 rounded-lg px-4 py-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                  <div
                    className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"
                    style={{ animationDelay: "0.1s" }}
                  ></div>
                  <div
                    className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"
                    style={{ animationDelay: "0.2s" }}
                  ></div>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Quick Replies */}
        <div className="border-t border-slate-200 p-4">
          <div className="flex flex-wrap gap-2 mb-4">
            {quickReplies.map((reply, index) => (
              <Button key={index} variant="outline" size="sm" onClick={() => setInputValue(reply)} className="text-xs">
                {reply}
              </Button>
            ))}
          </div>

          {/* Input Area */}
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon">
              <Paperclip className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon">
              <Mic className="h-4 w-4" />
            </Button>
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="输入您的问题..."
              onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
              className="flex-1"
            />
            <Button onClick={handleSendMessage} disabled={!inputValue.trim()}>
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
