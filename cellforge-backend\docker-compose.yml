version: '3.8'

services:
  # 后端 FastAPI 应用
  backend:
    build: .
    ports:
      - "8800:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://cellforge:cellforge123@postgres:5432/cellforge_ai
      - REDIS_URL=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SECRET_KEY=dev-secret-key-change-in-production
      - DEBUG=true
    depends_on:
      - postgres
      - redis
    volumes:
      - ./app:/app/app
      - ./uploads:/app/uploads
      - ./chroma_db:/app/chroma_db
    restart: unless-stopped
    networks:
      - cellforge-network

  # PostgreSQL 主数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=cellforge_ai
      - POSTGRES_USER=cellforge
      - POSTGRES_PASSWORD=cellforge123
    ports:
      - "15432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    restart: unless-stopped
    networks:
      - cellforge-network

  # Redis 缓存数据库
  redis:
    image: redis:7-alpine
    ports:
      - "16379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - cellforge-network

  # ChromaDB 向量数据库
  chromadb:
    image: chromadb/chroma:latest
    ports:
      - "8801:8000"
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    volumes:
      - chroma_data:/chroma/chroma
    restart: unless-stopped
    networks:
      - cellforge-network

  # Nginx 反向代理（生产环境）
  nginx:
    image: nginx:alpine
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - cellforge-network
    profiles:
      - production

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "19090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - cellforge-network
    profiles:
      - monitoring

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    ports:
      - "13001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - cellforge-network
    profiles:
      - monitoring

volumes:
  postgres_data:
  redis_data:
  chroma_data:
  prometheus_data:
  grafana_data:

networks:
  cellforge-network:
    driver: bridge 