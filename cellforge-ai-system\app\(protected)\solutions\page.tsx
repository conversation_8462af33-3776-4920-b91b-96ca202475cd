import { hasPermission } from "@/lib/auth"
import { redirect } from "next/navigation"
import SolutionPresentation from "./SolutionPresentation"
import { AccessDenied } from "@/components/access-denied"

const SolutionsPage = () => {
  if (!hasPermission("view_solutions")) {
    redirect("/login")
  }

  // Update the return statement to conditionally render
  return (
    <div className="min-h-[calc(100vh-64px)]">
      {hasPermission("view_solutions") ? <SolutionPresentation /> : <AccessDenied />}
    </div>
  )
}

export default SolutionsPage
