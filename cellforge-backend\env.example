# CellForge AI Backend 环境配置示例文件
# 复制此文件为 .env 并填入真实配置

# ===========================================
# 应用基础配置
# ===========================================
APP_NAME=CellForge AI Backend
DEBUG=true
LOG_LEVEL=INFO

# ===========================================
# 数据库配置 (端口已优化避免冲突)
# ===========================================
# PostgreSQL 主数据库 (端口: 15432)
DATABASE_URL=postgresql+asyncpg://cellforge:cellforge123@localhost:15432/cellforge_ai

# Redis 缓存数据库 (端口: 16379)
REDIS_URL=redis://localhost:16379/0

# ChromaDB 向量数据库 (端口: 8801)
CHROMA_HOST=localhost
CHROMA_PORT=8801
CHROMA_PERSIST_DIRECTORY=./chroma_db

# ===========================================
# 认证和安全配置
# ===========================================
# JWT 密钥 (生产环境请使用强密钥)
SECRET_KEY=your-super-secret-key-change-in-production-environment
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===========================================
# AI 服务配置
# ===========================================
# OpenAI API 配置
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.3

# LangChain 配置
LANGCHAIN_TRACING_V2=false
LANGCHAIN_API_KEY=your-langchain-api-key-if-needed

# ===========================================
# 文件存储配置
# ===========================================
UPLOAD_DIRECTORY=./uploads
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=pdf,docx,txt,xlsx,csv

# ===========================================
# 服务端口配置 (已优化避免冲突)
# ===========================================
# 后端应用端口
SERVER_HOST=0.0.0.0
SERVER_PORT=8800

# 数据库服务端口
POSTGRES_PORT=15432
REDIS_PORT=16379
CHROMA_PORT=8801

# 监控服务端口
PROMETHEUS_PORT=19090
GRAFANA_PORT=13001

# ===========================================
# 单细胞分析配置
# ===========================================
SCANPY_SETTINGS_VERBOSITY=1
SCANPY_SETTINGS_N_JOBS=-1
SCANPY_SETTINGS_CACHE_COMPRESSION='gzip'

# ===========================================
# 邮件服务配置
# ===========================================
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# ===========================================
# 缓存配置
# ===========================================
CACHE_TTL=3600  # 缓存过期时间(秒)
CACHE_MAX_SIZE=1000  # 最大缓存条目数

# ===========================================
# API 限流配置
# ===========================================
RATE_LIMIT_REQUESTS=100  # 每分钟请求数限制
RATE_LIMIT_WINDOW=60     # 时间窗口(秒)

# ===========================================
# 日志和监控配置
# ===========================================
# Sentry 错误监控
SENTRY_DSN=your-sentry-dsn-here
SENTRY_ENVIRONMENT=development

# 结构化日志配置
LOG_FORMAT=json
LOG_LEVEL=INFO

# ===========================================
# CORS 和安全配置
# ===========================================
# 允许的前端域名
ALLOWED_ORIGINS=http://localhost:3000,https://cellforge-ai.com
ALLOWED_HOSTS=localhost,127.0.0.1,cellforge-api.com

# Cookie 安全配置
COOKIE_SECURE=false  # 生产环境设为 true
COOKIE_SAMESITE=lax

# ===========================================
# 业务配置
# ===========================================
# 默认用户角色
DEFAULT_USER_ROLE=customer
DEFAULT_LANGUAGE=zh

# 知识库配置
KNOWLEDGE_MAX_CHUNKS=5
KNOWLEDGE_CHUNK_SIZE=1000
KNOWLEDGE_OVERLAP=200

# AI 回复配置
AI_MAX_TOKENS=2000
AI_TIMEOUT=30  # 秒

# ===========================================
# 开发环境特殊配置
# ===========================================
# 是否启用 API 文档
ENABLE_DOCS=true
DOCS_URL=/docs
REDOC_URL=/redoc

# 是否启用测试模式
TESTING=false
TEST_DATABASE_URL=postgresql+asyncpg://test:test@localhost:15432/cellforge_test 