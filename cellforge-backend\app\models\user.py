from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, Enum
from sqlalchemy.sql import func
from datetime import datetime
import enum

from app.core.database import Base


class UserRole(str, enum.Enum):
    SUPER_ADMIN = "super_admin"
    SALES = "sales"
    CUSTOMER = "customer"
    OPERATIONS = "operations"
    GUEST = "guest"


class UserStatus(str, enum.Enum):
    ACTIVE = "active"
    PENDING = "pending"
    SUSPENDED = "suspended"
    DELETED = "deleted"


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)

    # 用户信息
    first_name = Column(String(100))
    last_name = Column(String(100))
    phone = Column(String(20))
    organization = Column(String(255))
    department = Column(String(255))
    title = Column(String(255))

    # 用户权限和状态
    role = Column(Enum(UserRole), default=UserRole.CUSTOMER)
    status = Column(Enum(UserStatus), default=UserStatus.PENDING)
    is_verified = Column(Boolean, default=False)

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))

    # 用户偏好设置
    preferred_language = Column(String(10), default="zh")
    email_notifications = Column(Boolean, default=True)

    # 研究兴趣（针对研究者用户）
    research_interests = Column(Text)
    expertise_areas = Column(Text)  # JSON 格式存储

    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}', role='{self.role}')>"